@echo off
chcp 65001 >nul
title PGGame Scatter Predictor - Installer

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🎰 PGGame Scatter Predictor                   ║
echo ║                        Installer v1.0                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 Đang cài đặt PGGame Scatter Predictor...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Đang chạy với quyền Administrator
) else (
    echo ⚠️  Khuyến nghị chạy với quyền Administrator
    echo    Nhấn Ctrl+C để hủy và chạy lại với quyền Administrator
    echo    Hoặc nhấn Enter để tiếp tục...
    pause >nul
)

echo.
echo 📁 Tạo thư mục cài đặt...

REM Create installation directory
set INSTALL_DIR=C:\PGGame_Scatter_Predictor
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    echo ✅ Đã tạo thư mục: %INSTALL_DIR%
) else (
    echo ℹ️  Th<PERSON> mục đã tồn tại: %INSTALL_DIR%
)

echo.
echo 📦 Sao chép file...

REM Copy main executable
if exist "PGGame_Scatter_Predictor.exe" (
    copy "PGGame_Scatter_Predictor.exe" "%INSTALL_DIR%\" >nul
    echo ✅ Đã sao chép PGGame_Scatter_Predictor.exe
) else (
    echo ❌ Không tìm thấy PGGame_Scatter_Predictor.exe
    goto :error
)

REM Copy README if exists
if exist "README.txt" (
    copy "README.txt" "%INSTALL_DIR%\" >nul
    echo ✅ Đã sao chép README.txt
)

REM Copy config if exists
if exist "config.json" (
    copy "config.json" "%INSTALL_DIR%\" >nul
    echo ✅ Đã sao chép config.json
)

echo.
echo 🔗 Tạo shortcut...

REM Create desktop shortcut using PowerShell
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\PGGame Scatter Predictor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\PGGame_Scatter_Predictor.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Tool dự đoán scatter cho game slot PGGame'; $Shortcut.Save()}" 2>nul

if %errorLevel% == 0 (
    echo ✅ Đã tạo shortcut trên Desktop
) else (
    echo ⚠️  Không thể tạo shortcut tự động
    echo    Bạn có thể tạo shortcut thủ công từ:
    echo    %INSTALL_DIR%\PGGame_Scatter_Predictor.exe
)

echo.
echo 📋 Tạo Start Menu entry...

REM Create Start Menu shortcut
set START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs
if not exist "%START_MENU_DIR%\PGGame Scatter Predictor.lnk" (
    powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\PGGame Scatter Predictor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\PGGame_Scatter_Predictor.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Tool dự đoán scatter cho game slot PGGame'; $Shortcut.Save()}" 2>nul
    
    if %errorLevel% == 0 (
        echo ✅ Đã thêm vào Start Menu
    ) else (
        echo ⚠️  Không thể thêm vào Start Menu
    )
)

echo.
echo 🔧 Tạo uninstaller...

REM Create uninstaller
(
echo @echo off
echo chcp 65001 ^>nul
echo title PGGame Scatter Predictor - Uninstaller
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🎰 PGGame Scatter Predictor                   ║
echo ║                       Uninstaller v1.0                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Bạn có chắc muốn gỡ cài đặt PGGame Scatter Predictor?
echo    Dữ liệu game sẽ được giữ lại để bạn có thể khôi phục sau.
echo.
echo Nhấn Y để xác nhận, bất kỳ phím nào khác để hủy...
choice /c YN /n /m "Gỡ cài đặt? (Y/N): "
if errorlevel 2 goto :cancel
echo.
echo 🗑️  Đang gỡ cài đặt...
echo.
echo 📁 Xóa shortcuts...
del "%USERPROFILE%\Desktop\PGGame Scatter Predictor.lnk" 2^>nul
del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\PGGame Scatter Predictor.lnk" 2^>nul
echo ✅ Đã xóa shortcuts
echo.
echo 📦 Xóa file chương trình...
del "%INSTALL_DIR%\PGGame_Scatter_Predictor.exe" 2^>nul
del "%INSTALL_DIR%\README.txt" 2^>nul
del "%INSTALL_DIR%\uninstall.bat" 2^>nul
echo ✅ Đã xóa file chương trình
echo.
echo ℹ️  Giữ lại file dữ liệu:
echo    - scatter_data.db
echo    - config.json
echo.
echo ✅ Gỡ cài đặt hoàn tất!
echo    Thư mục %INSTALL_DIR% vẫn còn với dữ liệu của bạn.
echo    Bạn có thể xóa thủ công nếu muốn.
goto :end
:cancel
echo ❌ Đã hủy gỡ cài đặt.
:end
echo.
pause
) > "%INSTALL_DIR%\uninstall.bat"

echo ✅ Đã tạo uninstaller

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ✅ CÀI ĐẶT HOÀN TẤT!                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎯 Cách sử dụng:
echo    • Nhấp đúp vào shortcut trên Desktop
echo    • Hoặc tìm "PGGame Scatter Predictor" trong Start Menu
echo    • Hoặc chạy trực tiếp: %INSTALL_DIR%\PGGame_Scatter_Predictor.exe
echo.
echo 📖 Đọc hướng dẫn chi tiết trong README.txt
echo.
echo 🗑️  Để gỡ cài đặt: chạy %INSTALL_DIR%\uninstall.bat
echo.
echo ⚠️  Lưu ý quan trọng:
echo    • Tool chỉ mang tính tham khảo
echo    • Luôn chơi có trách nhiệm
echo    • Đặt giới hạn cho bản thân
echo.

REM Ask if user wants to run the program
echo 🚀 Bạn có muốn chạy chương trình ngay bây giờ không?
choice /c YN /n /m "Chạy ngay? (Y/N): "
if errorlevel 2 goto :skip_run

echo.
echo 🎰 Đang khởi động PGGame Scatter Predictor...
start "" "%INSTALL_DIR%\PGGame_Scatter_Predictor.exe"

:skip_run
echo.
echo 🍀 Chúc bạn may mắn!
echo.
pause
goto :end

:error
echo.
echo ❌ Cài đặt thất bại!
echo    Vui lòng kiểm tra lại file cài đặt.
echo.
pause

:end
