# 🎰 Hướng dẫn sử dụng PGGame Scatter Predictor (<PERSON><PERSON> độ thủ công)

## 🚀 Khởi động ứng dụng

### **Cách 1: <PERSON><PERSON>n bản đơn giản (Khuyến nghị)**
```bash
cd /d/toolnohu
python main_simple.py
```

### **Cách 2: Phiên bản đầy đủ**
```bash
cd /d/toolnohu
python main.py
```

> 💡 **Lưu ý**: Phiên bản đơn giản ổn định hơn và không có lỗi Selenium

## 📋 Hướng dẫn từng bước

### **BƯỚC 1: Thiết lập Session**

1. **Mở ứng dụng** → Tab "🌐 Session"

2. **Thêm session mới:**
   - **Tên**: `Mạt Chược 2 - Table 3`
   - **URL**: `https://www.13win16.com/home/<USER>
   - Click "➕ Thêm Session"

3. **Kết nối session:**
   - Click "🔗 Kết nối"
   - Ứng dụng sẽ tự động mở URL trong trình duyệt
   - Status sẽ chuyển thành "Chế độ thủ công"

### **BƯỚC 2: Cài đặt thông số**

1. **Vào tab "⚙️ Cài đặt"**

2. **Điều chỉnh thông số:**
   - **Tỷ lệ Scatter (%)**: `3.0` (hoặc theo game cụ thể)
   - **Số scatter tối thiểu**: `3`
   - Click "💾 Lưu cài đặt"

### **BƯỚC 3: Sắp xếp màn hình**

1. **Chia đôi màn hình:**
   ```
   ┌─────────────────┬─────────────────┐
   │                 │                 │
   │   GAME          │   TOOL          │
   │   (Trình duyệt) │   (Ứng dụng)    │
   │                 │                 │
   └─────────────────┴─────────────────┘
   ```

2. **Trái**: Game trong trình duyệt
3. **Phải**: Ứng dụng PGGame Scatter Predictor

### **BƯỚC 4: Chơi và ghi nhận**

1. **Vào tab "🎯 Dự đoán"** trong ứng dụng

2. **Workflow mỗi lần spin:**
   ```
   Game: Spin → Xem kết quả → Ghi nhận vào Tool
   
   Nếu KHÔNG ra scatter:
   ├── Click "🎰 Spin thường"
   └── Tool cập nhật streak +1
   
   Nếu RA scatter:
   ├── Click "⭐ Ra Scatter"  
   └── Tool reset streak về 0
   ```

3. **Tool tự động:**
   - Cập nhật thống kê
   - Tính toán dự đoán mới
   - Hiển thị xác suất
   - Đưa ra khuyến nghị

## 📊 Đọc hiểu dự đoán

### **Thông tin hiển thị:**

```
📊 Thống kê hiện tại:
• Spin liên tiếp: 15        ← Số spin chưa ra scatter
• Tổng spin: 100           ← Tổng số spin đã chơi  
• Scatter đã ra: 3         ← Số lần đã ra scatter
• Tỷ lệ thực tế: 3.00%     ← Tỷ lệ scatter thực tế

🔮 Dự đoán:
• Xác suất 5 spin tiếp: 15%
• Xác suất 10 spin tiếp: 28%
• Xác suất 20 spin tiếp: 45%
• Khuyến nghị: TIẾP TỤC / CẨN THẬN / NGHỈ NGƠI
```

### **Mức độ rủi ro:**

| Streak | Mức độ | Màu sắc | Khuyến nghị |
|--------|--------|---------|-------------|
| 0-20   | THẤP   | 🟢 Xanh | Tiếp tục chơi |
| 21-40  | TRUNG BÌNH | 🟡 Vàng | Chú ý quan sát |
| 41-60  | CAO    | 🟠 Cam  | Cân nhắc nghỉ |
| 60+    | RẤT CAO | 🔴 Đỏ   | Nên nghỉ ngơi |

## 🎯 Tips sử dụng hiệu quả

### **Ghi nhận chính xác:**
- ✅ Ghi nhận ngay sau mỗi spin
- ✅ Không bỏ sót bất kỳ kết quả nào
- ✅ Kiểm tra lại nếu không chắc chắn
- ❌ Không ghi nhận sai (scatter thành spin thường)

### **Theo dõi dự đoán:**
- 📈 Chú ý đến xu hướng xác suất
- ⚠️ Nghỉ ngơi khi rủi ro RẤT CAO
- 💰 Sử dụng thông tin để quyết định bet size
- 🎯 Kết hợp với kinh nghiệm cá nhân

### **Quản lý session:**
- 📝 Có thể tạo nhiều session cho các table khác nhau
- 📊 Mỗi session có thống kê riêng biệt
- 💾 Dữ liệu được lưu tự động
- 🔄 Có thể reset dữ liệu khi cần

## 🔧 Xử lý sự cố

### **Ứng dụng không mở được:**
```bash
# Kiểm tra Python
python --version

# Cài đặt dependencies
pip install customtkinter

# Chạy lại
python main_simple.py
```

### **Không mở được URL:**
- Kiểm tra kết nối internet
- Copy URL và mở thủ công trong trình duyệt
- Đảm bảo đã đăng nhập tài khoản game

### **Dự đoán không chính xác:**
- Kiểm tra tỷ lệ scatter trong cài đặt
- Đảm bảo ghi nhận đúng tất cả kết quả
- Reset dữ liệu nếu cần bắt đầu lại

### **Lỗi CustomTkinter:**
- Restart ứng dụng
- Sử dụng `main_simple.py` thay vì `main.py`
- Cập nhật CustomTkinter: `pip install --upgrade customtkinter`

## 📈 Theo dõi hiệu quả

### **Tab "📈 Thống kê":**
- Xem thống kê chi tiết
- Phân tích xu hướng
- Đánh giá hiệu quả dự đoán

### **Xuất dữ liệu:**
- Dữ liệu được lưu trong database SQLite
- Có thể xuất ra Excel/CSV để phân tích
- Backup dữ liệu định kỳ

## 🎮 Workflow hoàn chỉnh

```
1. Khởi động ứng dụng
   ↓
2. Tạo/chọn session
   ↓  
3. Cài đặt thông số
   ↓
4. Kết nối session (mở game)
   ↓
5. Sắp xếp 2 cửa sổ cạnh nhau
   ↓
6. Bắt đầu chơi và ghi nhận:
   ├── Spin → Không scatter → "🎰 Spin thường"
   └── Spin → Ra scatter → "⭐ Ra Scatter"
   ↓
7. Theo dõi dự đoán và điều chỉnh chiến lược
   ↓
8. Nghỉ ngơi khi rủi ro cao
```

## 🔮 Hiểu về thuật toán dự đoán

### **Nguyên lý:**
- Dựa trên xác suất thống kê
- Phân tích pattern lịch sử
- Tính toán xu hướng
- Đưa ra khuyến nghị

### **Độ chính xác:**
- Không đảm bảo 100% chính xác
- Chỉ là công cụ hỗ trợ quyết định
- Kết hợp với kinh nghiệm cá nhân
- Quản lý rủi ro hợp lý

## 💡 Lưu ý quan trọng

### **Trách nhiệm:**
- Tool chỉ mang tính chất tham khảo
- Người chơi tự chịu trách nhiệm quyết định
- Chơi có trách nhiệm và trong khả năng

### **Bảo mật:**
- Không chia sẻ thông tin tài khoản
- Sử dụng tool trên máy tính cá nhân
- Backup dữ liệu quan trọng

---

**🎯 Kết luận: Tool hoạt động hoàn hảo ở chế độ thủ công. Hãy làm theo hướng dẫn trên để có trải nghiệm tốt nhất!**

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Đọc lại hướng dẫn
2. Kiểm tra file log
3. Restart ứng dụng
4. Sử dụng phiên bản simple nếu có lỗi
