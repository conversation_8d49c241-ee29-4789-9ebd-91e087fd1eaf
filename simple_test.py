#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test without Selenium
Test đơn giản không cần Selenium
"""

import requests
import webbrowser
import time

def test_url_simple(url):
    """Test URL without Selenium"""
    print("🧪 Test URL đơn giản (không dùng Selenium)")
    print("=" * 50)
    print(f"🔗 URL: {url}")
    print()
    
    # Test 1: HTTP Request
    print("1️⃣ Test HTTP Request...")
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Content Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            print("   ✅ URL có thể truy cập")
            
            # <PERSON><PERSON>m tra nội dung
            content = response.text.lower()
            if 'game' in content or 'canvas' in content or 'iframe' in content:
                print("   ✅ Có vẻ như trang chứa game")
            else:
                print("   ⚠️ Không thấy dấu hiệu game rõ ràng")
                
        else:
            print(f"   ❌ Lỗi HTTP: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Lỗi request: {e}")
    
    # Test 2: Mở trình duyệt
    print("\n2️⃣ Test mở trình duyệt...")
    try:
        print("   🌐 Đang mở URL trong trình duyệt mặc định...")
        webbrowser.open(url)
        print("   ✅ Đã mở trình duyệt")
        print("   👀 Hãy kiểm tra xem game có load không")
        
        # Đợi user kiểm tra
        input("\n   ⏳ Nhấn Enter sau khi kiểm tra game trong trình duyệt...")
        
    except Exception as e:
        print(f"   ❌ Lỗi mở trình duyệt: {e}")

def manual_session_guide():
    """Hướng dẫn tạo session thủ công"""
    print("\n" + "="*60)
    print("📋 HƯỚNG DẪN TẠO SESSION THỦ CÔNG")
    print("="*60)
    
    print("""
🎯 Vì Selenium gặp vấn đề, bạn có thể sử dụng chế độ thủ công:

1️⃣ CÁCH 1: Ghi nhận thủ công
   • Mở ứng dụng PGGame Scatter Predictor
   • Vào tab "🎯 Dự đoán"
   • Mỗi lần spin:
     - Nhấn "🎰 Spin thường" nếu không ra scatter
     - Nhấn "⭐ Ra Scatter" nếu ra scatter

2️⃣ CÁCH 2: Khắc phục Selenium
   • Cài đặt Chrome mới nhất
   • Chạy: pip install --upgrade selenium webdriver-manager
   • Restart máy tính
   • Thử lại

3️⃣ CÁCH 3: Sử dụng Firefox
   • Cài đặt Firefox
   • Cài đặt: pip install geckodriver-autoinstaller
   • Sửa code để dùng Firefox thay vì Chrome

4️⃣ THÔNG TIN SESSION CỦA BẠN:
   • Tên: Mạt Chược 2 - Table 3
   • URL: https://www.13win16.com/home/<USER>
   • Game Type: PGGame
   • Trình duyệt: Chrome

💡 KHUYẾN NGHỊ:
   Hiện tại nên sử dụng chế độ thủ công (Cách 1) để theo dõi game
   trong khi khắc phục vấn đề Selenium.
""")

def main():
    """Main function"""
    url = "https://www.13win16.com/home/<USER>"
    
    print("🎰 PGGame Scatter Predictor - Simple Test")
    print("=" * 50)
    
    # Test URL
    test_url_simple(url)
    
    # Hướng dẫn
    manual_session_guide()
    
    print("\n✅ Test hoàn tất!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test bị hủy")
    except Exception as e:
        print(f"\n💥 Lỗi: {e}")
        import traceback
        traceback.print_exc()
