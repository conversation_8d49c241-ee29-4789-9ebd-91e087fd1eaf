#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scatter Predictor for PGGame
Module dự đoán scatter cho game slot PGGame
"""

import math
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import json

class ScatterPredictor:
    """Advanced scatter prediction algorithm"""
    
    def __init__(self, db_manager, config_manager):
        self.db_manager = db_manager
        self.config_manager = config_manager
        
    def get_prediction(self) -> str:
        """Get comprehensive prediction text"""
        stats = self.db_manager.get_current_stats()
        settings = self.config_manager.get_settings()
        
        current_streak = stats['current_streak']
        total_spins = stats['total_spins']
        total_scatters = stats['total_scatters']
        scatter_rate = settings.get('scatter_rate', 3.0) / 100.0
        
        # Basic prediction
        basic_prediction = self.calculate_basic_prediction(current_streak, scatter_rate)
        
        # Advanced prediction with streak analysis
        advanced_prediction = self.calculate_advanced_prediction(current_streak, scatter_rate, stats)
        
        # Probability analysis
        probabilities = self.calculate_probabilities(current_streak, scatter_rate)
        
        # Risk assessment
        risk_level = self.assess_risk(current_streak, scatter_rate)
        
        # Generate prediction text
        prediction_text = self.format_prediction(
            basic_prediction, advanced_prediction, probabilities, risk_level, stats
        )
        
        return prediction_text
    
    def calculate_basic_prediction(self, current_streak: int, scatter_rate: float) -> int:
        """Calculate basic prediction based on expected value"""
        if scatter_rate <= 0:
            return 999
        
        expected_spins = 1 / scatter_rate
        remaining_spins = max(1, int(expected_spins - current_streak))
        
        return remaining_spins
    
    def calculate_advanced_prediction(self, current_streak: int, scatter_rate: float, stats: Dict) -> Dict:
        """Calculate advanced prediction with multiple factors"""
        
        # Base expectation
        base_expectation = 1 / scatter_rate if scatter_rate > 0 else 100
        
        # Streak adjustment factor
        streak_factor = self.calculate_streak_factor(current_streak, scatter_rate)
        
        # Historical pattern analysis
        pattern_factor = self.analyze_historical_patterns(stats)
        
        # Time-based adjustment
        time_factor = self.calculate_time_factor()
        
        # Combined prediction
        adjusted_expectation = base_expectation * streak_factor * pattern_factor * time_factor
        predicted_spins = max(1, int(adjusted_expectation - current_streak))
        
        # Confidence calculation
        confidence = self.calculate_confidence(current_streak, scatter_rate, stats)
        
        return {
            'predicted_spins': predicted_spins,
            'confidence': confidence,
            'streak_factor': streak_factor,
            'pattern_factor': pattern_factor,
            'time_factor': time_factor
        }
    
    def calculate_streak_factor(self, current_streak: int, scatter_rate: float) -> float:
        """Calculate adjustment factor based on current streak"""
        expected_spins = 1 / scatter_rate if scatter_rate > 0 else 100
        
        if current_streak == 0:
            return 1.0
        
        # Gambler's fallacy adjustment (slight bias towards shorter prediction for long streaks)
        if current_streak > expected_spins * 2:
            return 0.8  # Slightly more optimistic for very long streaks
        elif current_streak > expected_spins:
            return 0.9  # Slightly more optimistic for long streaks
        else:
            return 1.0  # Normal expectation for short streaks
    
    def analyze_historical_patterns(self, stats: Dict) -> float:
        """Analyze historical patterns to adjust prediction"""
        total_spins = stats['total_spins']
        total_scatters = stats['total_scatters']
        
        if total_spins < 50:  # Not enough data
            return 1.0
        
        # Compare actual rate to expected rate
        actual_rate = total_scatters / total_spins if total_spins > 0 else 0
        settings = self.config_manager.get_settings()
        expected_rate = settings.get('scatter_rate', 3.0) / 100.0
        
        if actual_rate > expected_rate * 1.2:
            return 1.1  # Game seems more generous
        elif actual_rate < expected_rate * 0.8:
            return 0.9  # Game seems tighter
        else:
            return 1.0  # Normal behavior
    
    def calculate_time_factor(self) -> float:
        """Calculate time-based adjustment factor"""
        # Get hourly stats for pattern analysis
        hourly_stats = self.db_manager.get_hourly_stats(7)
        
        if not hourly_stats:
            return 1.0
        
        current_hour = datetime.now().hour
        
        # Find current hour stats
        current_hour_stats = None
        for stat in hourly_stats:
            if stat['hour'] == current_hour:
                current_hour_stats = stat
                break
        
        if not current_hour_stats:
            return 1.0
        
        # Calculate average rate for this hour vs overall average
        avg_rate = sum(stat['scatter_rate'] for stat in hourly_stats) / len(hourly_stats)
        current_rate = current_hour_stats['scatter_rate']
        
        if current_rate > avg_rate * 1.1:
            return 0.9  # This hour tends to be more generous
        elif current_rate < avg_rate * 0.9:
            return 1.1  # This hour tends to be tighter
        else:
            return 1.0
    
    def calculate_probabilities(self, current_streak: int, scatter_rate: float) -> Dict:
        """Calculate probabilities for different spin ranges"""
        probabilities = {}
        
        ranges = [5, 10, 20, 50, 100]
        
        for n in ranges:
            # P(at least 1 scatter in n spins) = 1 - (1 - rate)^n
            prob = 1 - (1 - scatter_rate) ** n
            probabilities[f'next_{n}'] = prob * 100
        
        return probabilities
    
    def calculate_confidence(self, current_streak: int, scatter_rate: float, stats: Dict) -> float:
        """Calculate confidence level of the prediction"""
        total_spins = stats['total_spins']
        
        # Base confidence starts high and decreases with uncertainty
        base_confidence = 0.8
        
        # Reduce confidence for very long streaks (more unpredictable)
        expected_spins = 1 / scatter_rate if scatter_rate > 0 else 100
        if current_streak > expected_spins * 3:
            base_confidence *= 0.6
        elif current_streak > expected_spins * 2:
            base_confidence *= 0.8
        
        # Increase confidence with more historical data
        if total_spins > 1000:
            base_confidence *= 1.1
        elif total_spins > 500:
            base_confidence *= 1.05
        elif total_spins < 50:
            base_confidence *= 0.7
        
        return min(1.0, base_confidence)
    
    def assess_risk(self, current_streak: int, scatter_rate: float) -> str:
        """Assess risk level based on current situation"""
        expected_spins = 1 / scatter_rate if scatter_rate > 0 else 100
        
        if current_streak < expected_spins * 0.5:
            return "THẤP"
        elif current_streak < expected_spins:
            return "TRUNG BÌNH"
        elif current_streak < expected_spins * 2:
            return "CAO"
        else:
            return "RẤT CAO"
    
    def format_prediction(self, basic_pred: int, advanced_pred: Dict, 
                         probabilities: Dict, risk_level: str, stats: Dict) -> str:
        """Format prediction into readable text"""
        
        text = "🔮 DỰ ĐOÁN SCATTER\n"
        text += "=" * 50 + "\n\n"
        
        # Current situation
        text += f"📊 TÌNH HÌNH HIỆN TẠI:\n"
        text += f"• Spin liên tiếp không scatter: {stats['current_streak']}\n"
        text += f"• Tổng số spin: {stats['total_spins']}\n"
        text += f"• Tỷ lệ thực tế: {stats['actual_rate']:.2f}%\n"
        text += f"• Streak dài nhất: {stats['longest_streak']}\n\n"
        
        # Predictions
        text += f"🎯 DỰ ĐOÁN:\n"
        text += f"• Dự đoán cơ bản: {basic_pred} spin\n"
        text += f"• Dự đoán nâng cao: {advanced_pred['predicted_spins']} spin\n"
        text += f"• Độ tin cậy: {advanced_pred['confidence']*100:.1f}%\n\n"
        
        # Risk assessment
        text += f"⚠️ MỨC ĐỘ RỦI RO: {risk_level}\n\n"
        
        # Probabilities
        text += f"📈 XÁC SUẤT RA SCATTER:\n"
        text += f"• Trong 5 spin tiếp: {probabilities['next_5']:.1f}%\n"
        text += f"• Trong 10 spin tiếp: {probabilities['next_10']:.1f}%\n"
        text += f"• Trong 20 spin tiếp: {probabilities['next_20']:.1f}%\n"
        text += f"• Trong 50 spin tiếp: {probabilities['next_50']:.1f}%\n\n"
        
        # Recommendations
        text += f"💡 KHUYẾN NGHỊ:\n"
        recommendations = self.get_recommendations(stats['current_streak'], risk_level, advanced_pred['confidence'])
        for rec in recommendations:
            text += f"• {rec}\n"
        
        text += f"\n⏰ Cập nhật lúc: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}"
        
        return text
    
    def get_recommendations(self, current_streak: int, risk_level: str, confidence: float) -> List[str]:
        """Get recommendations based on current situation"""
        recommendations = []
        
        if risk_level == "THẤP":
            recommendations.append("Tình hình bình thường, có thể tiếp tục chơi")
            recommendations.append("Đặt cược ở mức an toàn")
        elif risk_level == "TRUNG BÌNH":
            recommendations.append("Cần thận trọng, theo dõi thêm vài spin")
            recommendations.append("Có thể tăng nhẹ cược nếu tự tin")
        elif risk_level == "CAO":
            recommendations.append("Streak đang dài, cần cân nhắc kỹ")
            recommendations.append("Nên giảm cược hoặc nghỉ ngơi")
        else:  # RẤT CAO
            recommendations.append("Streak rất dài, rủi ro cao")
            recommendations.append("Khuyến nghị nghỉ ngơi hoặc đổi game")
        
        if confidence < 0.5:
            recommendations.append("Độ tin cậy thấp, cần thêm dữ liệu")
        
        recommendations.append("Luôn chơi có trách nhiệm và đặt giới hạn")
        
        return recommendations
    
    def get_streak_analysis(self) -> Dict:
        """Get detailed streak analysis"""
        distribution = self.db_manager.get_streak_distribution()
        
        if not distribution:
            return {"message": "Chưa có đủ dữ liệu để phân tích"}
        
        # Calculate statistics
        streaks = list(distribution.keys())
        counts = list(distribution.values())
        
        avg_streak = sum(s * c for s, c in zip(streaks, counts)) / sum(counts)
        max_streak = max(streaks)
        most_common_streak = max(distribution, key=distribution.get)
        
        return {
            'average_streak': avg_streak,
            'max_streak': max_streak,
            'most_common_streak': most_common_streak,
            'distribution': distribution,
            'total_scatters': sum(counts)
        }
    
    def predict_next_scatter_time(self, current_streak: int, scatter_rate: float) -> Dict:
        """Predict when the next scatter might occur"""
        # Calculate different scenarios
        scenarios = {
            'optimistic': max(1, int((1 / scatter_rate) * 0.5 - current_streak)),
            'realistic': max(1, int((1 / scatter_rate) - current_streak)),
            'pessimistic': max(1, int((1 / scatter_rate) * 1.5 - current_streak))
        }
        
        # Calculate probabilities for each scenario
        for scenario, spins in scenarios.items():
            prob = 1 - (1 - scatter_rate) ** spins
            scenarios[f'{scenario}_probability'] = prob * 100
        
        return scenarios
