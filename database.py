#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Manager for PGGame Scatter Predictor
Quản lý cơ sở dữ liệu cho tool dự đoán scatter
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import threading

class DatabaseManager:
    """Manages SQLite database operations for the scatter predictor"""
    
    def __init__(self, db_path: str = "scatter_data.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """Initialize database tables"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create spins table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS spins (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_scatter BOOLEAN NOT NULL,
                    streak_before INTEGER DEFAULT 0,
                    game_session TEXT DEFAULT '',
                    notes TEXT DEFAULT ''
                )
            ''')
            
            # Create sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    end_time DATETIME,
                    total_spins INTEGER DEFAULT 0,
                    total_scatters INTEGER DEFAULT 0,
                    max_streak INTEGER DEFAULT 0,
                    notes TEXT DEFAULT ''
                )
            ''')
            
            # Create predictions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    current_streak INTEGER,
                    predicted_spins INTEGER,
                    confidence REAL,
                    actual_result INTEGER,
                    accuracy REAL
                )
            ''')
            
            # Create settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
    
    def add_spin_result(self, is_scatter: bool, session_id: str = "", notes: str = "") -> int:
        """Add a spin result to the database"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get current streak
            current_streak = self.get_current_streak()
            
            # Insert spin result
            cursor.execute('''
                INSERT INTO spins (is_scatter, streak_before, game_session, notes)
                VALUES (?, ?, ?, ?)
            ''', (is_scatter, current_streak, session_id, notes))
            
            spin_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return spin_id
    
    def get_current_streak(self) -> int:
        """Get current streak of spins without scatter"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get the most recent spins
        cursor.execute('''
            SELECT is_scatter FROM spins 
            ORDER BY timestamp DESC 
            LIMIT 100
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        streak = 0
        for (is_scatter,) in results:
            if is_scatter:
                break
            streak += 1
        
        return streak
    
    def get_current_stats(self) -> Dict:
        """Get current statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get total spins and scatters
        cursor.execute('SELECT COUNT(*) FROM spins')
        total_spins = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM spins WHERE is_scatter = 1')
        total_scatters = cursor.fetchone()[0]
        
        # Get current streak
        current_streak = self.get_current_streak()
        
        # Get longest streak
        cursor.execute('''
            SELECT MAX(streak_before) FROM spins WHERE is_scatter = 1
        ''')
        result = cursor.fetchone()
        longest_streak = result[0] if result[0] is not None else 0
        
        # Get average spins per scatter
        avg_spins_per_scatter = 0
        if total_scatters > 0:
            avg_spins_per_scatter = total_spins / total_scatters
        
        conn.close()
        
        return {
            'total_spins': total_spins,
            'total_scatters': total_scatters,
            'current_streak': current_streak,
            'longest_streak': longest_streak,
            'avg_spins_per_scatter': avg_spins_per_scatter,
            'actual_rate': (total_scatters / total_spins * 100) if total_spins > 0 else 0
        }
    
    def get_recent_history(self, limit: int = 50) -> List[Dict]:
        """Get recent spin history"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT timestamp, is_scatter, streak_before, notes
            FROM spins 
            ORDER BY timestamp DESC 
            LIMIT ?
        ''', (limit,))
        
        results = cursor.fetchall()
        conn.close()
        
        history = []
        for timestamp, is_scatter, streak_before, notes in results:
            history.append({
                'timestamp': timestamp,
                'is_scatter': bool(is_scatter),
                'streak_before': streak_before,
                'notes': notes
            })
        
        return history
    
    def get_streak_distribution(self) -> Dict[int, int]:
        """Get distribution of streak lengths"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT streak_before, COUNT(*) 
            FROM spins 
            WHERE is_scatter = 1 
            GROUP BY streak_before 
            ORDER BY streak_before
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        distribution = {}
        for streak, count in results:
            distribution[streak] = count
        
        return distribution
    
    def get_hourly_stats(self, days: int = 7) -> List[Dict]:
        """Get hourly statistics for the last N days"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        start_date = datetime.now() - timedelta(days=days)
        
        cursor.execute('''
            SELECT 
                strftime('%H', timestamp) as hour,
                COUNT(*) as total_spins,
                SUM(is_scatter) as total_scatters
            FROM spins 
            WHERE timestamp >= ?
            GROUP BY strftime('%H', timestamp)
            ORDER BY hour
        ''', (start_date,))
        
        results = cursor.fetchall()
        conn.close()
        
        hourly_stats = []
        for hour, total_spins, total_scatters in results:
            hourly_stats.append({
                'hour': int(hour),
                'total_spins': total_spins,
                'total_scatters': total_scatters,
                'scatter_rate': (total_scatters / total_spins * 100) if total_spins > 0 else 0
            })
        
        return hourly_stats
    
    def add_prediction(self, current_streak: int, predicted_spins: int, confidence: float):
        """Add a prediction record"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO predictions (current_streak, predicted_spins, confidence)
                VALUES (?, ?, ?)
            ''', (current_streak, predicted_spins, confidence))
            
            conn.commit()
            conn.close()
    
    def update_prediction_accuracy(self, prediction_id: int, actual_result: int):
        """Update prediction accuracy when result is known"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get the prediction
            cursor.execute('''
                SELECT predicted_spins FROM predictions WHERE id = ?
            ''', (prediction_id,))
            
            result = cursor.fetchone()
            if result:
                predicted_spins = result[0]
                accuracy = 1.0 - abs(predicted_spins - actual_result) / max(predicted_spins, actual_result)
                
                cursor.execute('''
                    UPDATE predictions 
                    SET actual_result = ?, accuracy = ?
                    WHERE id = ?
                ''', (actual_result, accuracy, prediction_id))
                
                conn.commit()
            
            conn.close()
    
    def get_prediction_accuracy(self) -> float:
        """Get average prediction accuracy"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT AVG(accuracy) FROM predictions 
            WHERE accuracy IS NOT NULL
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result[0] is not None else 0.0
    
    def reset_data(self):
        """Reset all data in the database"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM spins')
            cursor.execute('DELETE FROM sessions')
            cursor.execute('DELETE FROM predictions')
            
            conn.commit()
            conn.close()
    
    def export_data(self, filepath: str):
        """Export data to JSON file"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all data
        cursor.execute('SELECT * FROM spins ORDER BY timestamp')
        spins = cursor.fetchall()
        
        cursor.execute('SELECT * FROM sessions ORDER BY start_time')
        sessions = cursor.fetchall()
        
        cursor.execute('SELECT * FROM predictions ORDER BY timestamp')
        predictions = cursor.fetchall()
        
        conn.close()
        
        # Create export data
        export_data = {
            'export_date': datetime.now().isoformat(),
            'spins': spins,
            'sessions': sessions,
            'predictions': predictions
        }
        
        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    def import_data(self, filepath: str):
        """Import data from JSON file"""
        with open(filepath, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Clear existing data
            cursor.execute('DELETE FROM spins')
            cursor.execute('DELETE FROM sessions')
            cursor.execute('DELETE FROM predictions')
            
            # Import spins
            for spin in import_data.get('spins', []):
                cursor.execute('''
                    INSERT INTO spins (id, timestamp, is_scatter, streak_before, game_session, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', spin)
            
            # Import sessions
            for session in import_data.get('sessions', []):
                cursor.execute('''
                    INSERT INTO sessions (id, start_time, end_time, total_spins, total_scatters, max_streak, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', session)
            
            # Import predictions
            for prediction in import_data.get('predictions', []):
                cursor.execute('''
                    INSERT INTO predictions (id, timestamp, current_streak, predicted_spins, confidence, actual_result, accuracy)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', prediction)
            
            conn.commit()
            conn.close()
