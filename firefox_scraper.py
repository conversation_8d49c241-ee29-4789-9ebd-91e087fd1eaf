#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Firefox version of scraper
Phiên bản Firefox của scraper
"""

import time
from selenium import webdriver
from selenium.webdriver.firefox.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class FirefoxGameScraper:
    """Firefox-based game scraper"""
    
    def __init__(self):
        self.driver = None
        self.is_running = False
    
    def setup_driver(self, headless: bool = False) -> bool:
        """Setup Firefox driver"""
        try:
            print("🦊 Đang thiết lập Firefox WebDriver...")
            
            options = Options()
            if headless:
                options.add_argument('--headless')
            
            # Firefox options
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.set_preference("dom.webdriver.enabled", False)
            options.set_preference('useAutomationExtension', False)
            
            # Cài đặt geckodriver tự động
            try:
                from webdriver_manager.firefox import GeckoDriverManager
                from selenium.webdriver.firefox.service import Service
                
                service = Service(GeckoDriverManager().install())
                self.driver = webdriver.Firefox(service=service, options=options)
            except ImportError:
                print("⚠️ webdriver-manager không có, thử cách khác...")
                self.driver = webdriver.Firefox(options=options)
            
            print("✅ Firefox WebDriver đã sẵn sàng")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi thiết lập Firefox: {e}")
            print("💡 Hãy cài đặt Firefox và geckodriver")
            return False
    
    def navigate_to_game(self, url: str) -> bool:
        """Navigate to game URL"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False
            
            print(f"🌐 Đang điều hướng đến: {url}")
            self.driver.get(url)
            
            print("⏳ Đang đợi trang load...")
            time.sleep(5)
            
            # Kiểm tra trang đã load
            try:
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                print("✅ Trang web đã load")
            except:
                print("⚠️ Trang web load chậm...")
            
            # Tìm game elements
            selectors = ["canvas", "iframe", "#game-canvas", ".game-area"]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ Tìm thấy game element: {selector}")
                        break
                except:
                    continue
            
            print("✅ Điều hướng hoàn tất")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi điều hướng: {e}")
            return False
    
    def cleanup(self):
        """Clean up"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        self.driver = None

def test_firefox():
    """Test Firefox scraper"""
    url = "https://www.13win16.com/home/<USER>"
    
    print("🦊 Test Firefox Scraper")
    print("=" * 40)
    
    scraper = FirefoxGameScraper()
    
    try:
        success = scraper.navigate_to_game(url)
        
        if success:
            print("\n✅ FIREFOX KẾT NỐI THÀNH CÔNG!")
            print("🎮 Có thể sử dụng Firefox thay vì Chrome")
            
            # Đợi để quan sát
            input("\nNhấn Enter để đóng...")
        else:
            print("\n❌ Firefox cũng không hoạt động")
            
    except Exception as e:
        print(f"\n💥 Lỗi: {e}")
        
    finally:
        scraper.cleanup()

if __name__ == "__main__":
    test_firefox()
