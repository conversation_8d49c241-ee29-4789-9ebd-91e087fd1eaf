#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Run script for PGGame Scatter Predictor
Script chạy tool dự đoán scatter với error handling
"""

import sys
import os
import traceback
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 hoặc cao hơn là bắt buộc")
        print(f"Phiên bản hiện tại: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are available"""
    required_modules = [
        ('customtkinter', 'customtkinter'),
        ('tkinter', 'tkinter'),
        ('sqlite3', 'sqlite3'),
        ('numpy', 'numpy'),
        ('matplotlib', 'matplotlib'),
        ('pandas', 'pandas')
    ]
    
    missing_modules = []
    
    for module_name, import_name in required_modules:
        try:
            __import__(import_name)
        except ImportError:
            missing_modules.append(module_name)
    
    if missing_modules:
        print("❌ Thiếu các module sau:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\nCài đặt bằng lệnh: pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """Setup environment for the application"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Create necessary directories
    directories = ['assets', 'assets/templates', 'backups', 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    return True

def run_application():
    """Run the main application"""
    try:
        print("🎰 Khởi động PGGame Scatter Predictor...")
        
        # Import and run the main application
        from main import PGGameScatterTool
        
        app = PGGameScatterTool()
        app.run()
        
    except ImportError as e:
        print(f"❌ Lỗi import module: {e}")
        print("Vui lòng kiểm tra lại các dependencies")
        return False
    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        print("\nChi tiết lỗi:")
        traceback.print_exc()
        return False
    
    return True

def main():
    """Main entry point"""
    print("🎰 PGGame Scatter Predictor")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        input("Nhấn Enter để thoát...")
        return False
    
    # Check dependencies
    if not check_dependencies():
        input("Nhấn Enter để thoát...")
        return False
    
    # Setup environment
    if not setup_environment():
        print("❌ Không thể thiết lập môi trường")
        input("Nhấn Enter để thoát...")
        return False
    
    print("✅ Kiểm tra hoàn tất, đang khởi động ứng dụng...")
    
    # Run application
    success = run_application()
    
    if not success:
        input("Nhấn Enter để thoát...")
    
    return success

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Tạm biệt!")
    except Exception as e:
        print(f"❌ Lỗi nghiêm trọng: {e}")
        traceback.print_exc()
        input("Nhấn Enter để thoát...")
