#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PGGame Scatter Predictor Tool
Tool dự đoán tỉ lệ ra scatter cho game slot PGGame
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
import sqlite3
import json
import os
import sys
from datetime import datetime, timedelta
import threading
import time
from typing import Dict, List, Tuple, Optional
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    print("Warning: Matplotlib not available")
    MATPLOTLIB_AVAILABLE = False

import pandas as pd
import numpy as np

# Import custom modules
from database import DatabaseManager
from predictor import ScatterPredictor
from config import ConfigManager
from scraper import GameScraper

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class PGGameScatterTool:
    """Main application class for PGGame Scatter Prediction Tool"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🎰 PGGame Scatter Predictor")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Initialize managers
        self.db_manager = DatabaseManager()
        self.config_manager = ConfigManager()
        self.predictor = ScatterPredictor(self.db_manager, self.config_manager)
        self.scraper = GameScraper()
        
        # Variables
        self.current_streak = tk.IntVar(value=0)
        self.total_spins = tk.IntVar(value=0)
        self.total_scatters = tk.IntVar(value=0)
        self.scatter_rate = tk.DoubleVar(value=3.0)
        self.min_scatters = tk.IntVar(value=3)
        self.auto_mode = tk.BooleanVar(value=False)
        
        # Setup UI
        self.setup_ui()
        self.load_data()
        self.load_sessions()
        self.update_stats()
        
        # Auto-update thread
        self.auto_update_thread = None
        self.running = True
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Create main container
        self.main_container = ctk.CTkFrame(self.root)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        self.notebook = ctk.CTkTabview(self.main_container)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create tabs
        self.setup_main_tab()
        self.setup_settings_tab()
        self.setup_stats_tab()
        self.setup_history_tab()
        self.setup_session_tab()
        self.setup_auto_tab()
        
    def setup_main_tab(self):
        """Setup main prediction tab"""
        main_tab = self.notebook.add("🎯 Dự đoán")
        
        # Current stats frame
        stats_frame = ctk.CTkFrame(main_tab)
        stats_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(stats_frame, text="📊 Thống kê hiện tại", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Stats grid
        stats_grid = ctk.CTkFrame(stats_frame)
        stats_grid.pack(fill="x", padx=10, pady=5)
        
        # Row 1
        ctk.CTkLabel(stats_grid, text="Spin liên tiếp:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        self.streak_label = ctk.CTkLabel(stats_grid, text="0", font=ctk.CTkFont(size=16, weight="bold"))
        self.streak_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        ctk.CTkLabel(stats_grid, text="Tổng spin:").grid(row=0, column=2, padx=10, pady=5, sticky="w")
        self.total_spins_label = ctk.CTkLabel(stats_grid, text="0", font=ctk.CTkFont(size=16, weight="bold"))
        self.total_spins_label.grid(row=0, column=3, padx=10, pady=5, sticky="w")
        
        # Row 2
        ctk.CTkLabel(stats_grid, text="Scatter đã ra:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.scatters_label = ctk.CTkLabel(stats_grid, text="0", font=ctk.CTkFont(size=16, weight="bold"))
        self.scatters_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        ctk.CTkLabel(stats_grid, text="Tỷ lệ thực tế:").grid(row=1, column=2, padx=10, pady=5, sticky="w")
        self.actual_rate_label = ctk.CTkLabel(stats_grid, text="0%", font=ctk.CTkFont(size=16, weight="bold"))
        self.actual_rate_label.grid(row=1, column=3, padx=10, pady=5, sticky="w")
        
        # Prediction frame
        prediction_frame = ctk.CTkFrame(main_tab)
        prediction_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(prediction_frame, text="🔮 Dự đoán", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Prediction display
        self.prediction_text = ctk.CTkTextbox(prediction_frame, height=150)
        self.prediction_text.pack(fill="x", padx=10, pady=5)
        
        # Action buttons frame
        actions_frame = ctk.CTkFrame(main_tab)
        actions_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(actions_frame, text="🎰 Ghi nhận kết quả", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        buttons_frame = ctk.CTkFrame(actions_frame)
        buttons_frame.pack(pady=10)
        
        # Action buttons
        ctk.CTkButton(buttons_frame, text="🎰 Spin thường", 
                     command=self.record_normal_spin,
                     width=150, height=40).pack(side="left", padx=10)
        
        ctk.CTkButton(buttons_frame, text="⭐ Ra Scatter", 
                     command=self.record_scatter,
                     width=150, height=40).pack(side="left", padx=10)
        
        ctk.CTkButton(buttons_frame, text="🎯 Tính toán dự đoán", 
                     command=self.calculate_prediction,
                     width=150, height=40).pack(side="left", padx=10)
        
        ctk.CTkButton(buttons_frame, text="🔄 Reset dữ liệu", 
                     command=self.reset_data,
                     width=150, height=40).pack(side="left", padx=10)

    def setup_settings_tab(self):
        """Setup settings tab"""
        settings_tab = self.notebook.add("⚙️ Cài đặt")
        
        # Game settings frame
        game_frame = ctk.CTkFrame(settings_tab)
        game_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(game_frame, text="🎮 Cài đặt Game", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Scatter rate setting
        rate_frame = ctk.CTkFrame(game_frame)
        rate_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(rate_frame, text="Tỷ lệ Scatter (%):").pack(side="left", padx=10)
        self.rate_entry = ctk.CTkEntry(rate_frame, textvariable=self.scatter_rate, width=100)
        self.rate_entry.pack(side="left", padx=10)
        
        # Min scatters setting
        min_frame = ctk.CTkFrame(game_frame)
        min_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(min_frame, text="Số scatter tối thiểu:").pack(side="left", padx=10)
        self.min_entry = ctk.CTkEntry(min_frame, textvariable=self.min_scatters, width=100)
        self.min_entry.pack(side="left", padx=10)
        
        # Save button
        ctk.CTkButton(game_frame, text="💾 Lưu cài đặt", 
                     command=self.save_settings).pack(pady=10)

    def setup_stats_tab(self):
        """Setup statistics tab"""
        stats_tab = self.notebook.add("📈 Thống kê")

        # Create scrollable frame
        self.stats_scroll = ctk.CTkScrollableFrame(stats_tab)
        self.stats_scroll.pack(fill="both", expand=True, padx=10, pady=10)

        # Detailed stats frame
        detailed_frame = ctk.CTkFrame(self.stats_scroll)
        detailed_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(detailed_frame, text="📊 Thống kê chi tiết",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)

        # Stats will be populated in update_detailed_stats method
        self.detailed_stats_frame = ctk.CTkFrame(detailed_frame)
        self.detailed_stats_frame.pack(fill="x", padx=10, pady=5)

        # Chart frame
        chart_frame = ctk.CTkFrame(self.stats_scroll)
        chart_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(chart_frame, text="📈 Biểu đồ",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)

        # Refresh stats button
        ctk.CTkButton(self.stats_scroll, text="🔄 Cập nhật thống kê",
                     command=self.update_detailed_stats).pack(pady=10)
        
    def setup_history_tab(self):
        """Setup history tab"""
        history_tab = self.notebook.add("📝 Lịch sử")
        
        # History display
        self.history_text = ctk.CTkTextbox(history_tab, height=400)
        self.history_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Refresh button
        ctk.CTkButton(history_tab, text="🔄 Làm mới",
                     command=self.refresh_history).pack(pady=10)

    def setup_session_tab(self):
        """Setup session management tab"""
        session_tab = self.notebook.add("🌐 Session Web")

        # Session management frame
        session_frame = ctk.CTkFrame(session_tab)
        session_frame.pack(fill="both", expand=True, padx=10, pady=5)

        ctk.CTkLabel(session_frame, text="🌐 Quản lý Session Game",
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)

        # Add session frame
        add_frame = ctk.CTkFrame(session_frame)
        add_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(add_frame, text="➕ Thêm Session Mới",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Session name
        name_frame = ctk.CTkFrame(add_frame)
        name_frame.pack(fill="x", padx=10, pady=2)
        ctk.CTkLabel(name_frame, text="Tên Session:").pack(side="left", padx=5)
        self.session_name_entry = ctk.CTkEntry(name_frame, placeholder_text="Ví dụ: Mạt Chược 2 - Table 1")
        self.session_name_entry.pack(side="right", fill="x", expand=True, padx=5)

        # Game URL
        url_frame = ctk.CTkFrame(add_frame)
        url_frame.pack(fill="x", padx=10, pady=2)
        ctk.CTkLabel(url_frame, text="URL Game:").pack(side="left", padx=5)
        self.session_url_entry = ctk.CTkEntry(url_frame, placeholder_text="https://game.example.com/...")
        self.session_url_entry.pack(side="right", fill="x", expand=True, padx=5)

        # Game type
        type_frame = ctk.CTkFrame(add_frame)
        type_frame.pack(fill="x", padx=10, pady=2)
        ctk.CTkLabel(type_frame, text="Loại Game:").pack(side="left", padx=5)
        self.game_type_var = tk.StringVar(value="PGGame")
        game_type_menu = ctk.CTkOptionMenu(type_frame, variable=self.game_type_var,
                                          values=["PGGame", "Pragmatic Play", "Evolution", "Khác"])
        game_type_menu.pack(side="right", padx=5)

        # Browser info
        browser_frame = ctk.CTkFrame(add_frame)
        browser_frame.pack(fill="x", padx=10, pady=2)
        ctk.CTkLabel(browser_frame, text="Trình duyệt:").pack(side="left", padx=5)
        self.browser_var = tk.StringVar(value="Chrome")
        browser_menu = ctk.CTkOptionMenu(browser_frame, variable=self.browser_var,
                                        values=["Chrome", "Firefox", "Edge", "Safari"])
        browser_menu.pack(side="right", padx=5)

        # Add button
        ctk.CTkButton(add_frame, text="➕ Thêm Session",
                     command=self.add_session).pack(pady=10)

        # Session list frame
        list_frame = ctk.CTkFrame(session_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)

        ctk.CTkLabel(list_frame, text="📋 Danh sách Session",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Session list
        self.session_listbox = ctk.CTkScrollableFrame(list_frame)
        self.session_listbox.pack(fill="both", expand=True, padx=10, pady=5)

        # Control buttons
        control_frame = ctk.CTkFrame(list_frame)
        control_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(control_frame, text="🔗 Kết nối",
                     command=self.connect_session, width=100).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="⏸️ Tạm dừng",
                     command=self.pause_session, width=100).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="🗑️ Xóa",
                     command=self.delete_session, width=100).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="🔄 Làm mới",
                     command=self.refresh_sessions, width=100).pack(side="left", padx=5)

        # Status frame
        status_frame = ctk.CTkFrame(session_frame)
        status_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(status_frame, text="📊 Trạng thái",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        self.session_status = ctk.CTkTextbox(status_frame, height=100)
        self.session_status.pack(fill="x", padx=10, pady=5)

        # Initialize session list
        self.sessions = []
        self.active_session = None
        self.refresh_sessions()

    def setup_auto_tab(self):
        """Setup auto mode tab"""
        auto_tab = self.notebook.add("🤖 Tự động")
        
        # Auto mode frame
        auto_frame = ctk.CTkFrame(auto_tab)
        auto_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(auto_frame, text="🤖 Chế độ tự động", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Auto mode toggle
        self.auto_switch = ctk.CTkSwitch(auto_frame, text="Bật chế độ tự động", 
                                        variable=self.auto_mode,
                                        command=self.toggle_auto_mode)
        self.auto_switch.pack(pady=10)
        
        # Status display
        self.auto_status = ctk.CTkTextbox(auto_frame, height=200)
        self.auto_status.pack(fill="x", padx=10, pady=5)

    def record_normal_spin(self):
        """Record a normal spin (no scatter)"""
        self.db_manager.add_spin_result(False)
        self.current_streak.set(self.current_streak.get() + 1)
        self.total_spins.set(self.total_spins.get() + 1)
        self.update_stats()
        self.calculate_prediction()
        
    def record_scatter(self):
        """Record a scatter result"""
        self.db_manager.add_spin_result(True)
        self.current_streak.set(0)
        self.total_spins.set(self.total_spins.get() + 1)
        self.total_scatters.set(self.total_scatters.get() + 1)
        self.update_stats()
        self.calculate_prediction()

    def calculate_prediction(self):
        """Calculate and display prediction"""
        prediction = self.predictor.get_prediction()
        self.prediction_text.delete("1.0", "end")
        self.prediction_text.insert("1.0", prediction)

    def update_stats(self):
        """Update statistics display"""
        # Update labels
        self.streak_label.configure(text=str(self.current_streak.get()))
        self.total_spins_label.configure(text=str(self.total_spins.get()))
        self.scatters_label.configure(text=str(self.total_scatters.get()))
        
        # Calculate actual rate
        if self.total_spins.get() > 0:
            actual_rate = (self.total_scatters.get() / self.total_spins.get()) * 100
            self.actual_rate_label.configure(text=f"{actual_rate:.2f}%")
        else:
            self.actual_rate_label.configure(text="0%")

    def load_data(self):
        """Load data from database"""
        stats = self.db_manager.get_current_stats()
        self.current_streak.set(stats.get('current_streak', 0))
        self.total_spins.set(stats.get('total_spins', 0))
        self.total_scatters.set(stats.get('total_scatters', 0))
        
        # Load settings
        settings = self.config_manager.get_settings()
        self.scatter_rate.set(settings.get('scatter_rate', 3.0))
        self.min_scatters.set(settings.get('min_scatters', 3))

    def save_settings(self):
        """Save current settings"""
        settings = {
            'scatter_rate': self.scatter_rate.get(),
            'min_scatters': self.min_scatters.get()
        }
        self.config_manager.save_settings(settings)
        messagebox.showinfo("Thành công", "Đã lưu cài đặt!")

    def reset_data(self):
        """Reset all data"""
        if messagebox.askyesno("Xác nhận", "Bạn có chắc muốn xóa tất cả dữ liệu?"):
            self.db_manager.reset_data()
            self.current_streak.set(0)
            self.total_spins.set(0)
            self.total_scatters.set(0)
            self.update_stats()
            messagebox.showinfo("Thành công", "Đã reset dữ liệu!")

    def refresh_history(self):
        """Refresh history display"""
        history = self.db_manager.get_recent_history(50)
        self.history_text.delete("1.0", "end")
        for record in history:
            timestamp = record['timestamp']
            result = "⭐ Scatter" if record['is_scatter'] else "🎰 Spin thường"
            self.history_text.insert("end", f"{timestamp}: {result}\n")

    def toggle_auto_mode(self):
        """Toggle auto mode on/off"""
        if self.auto_mode.get():
            self.start_auto_mode()
        else:
            self.stop_auto_mode()

    def start_auto_mode(self):
        """Start auto mode"""
        self.auto_status.insert("end", "🤖 Bắt đầu chế độ tự động...\n")
        # Implementation for auto scraping would go here

    def stop_auto_mode(self):
        """Stop auto mode"""
        self.auto_status.insert("end", "⏹️ Dừng chế độ tự động\n")

    def add_session(self):
        """Add new session"""
        name = self.session_name_entry.get().strip()
        url = self.session_url_entry.get().strip()
        game_type = self.game_type_var.get()
        browser = self.browser_var.get()

        if not name or not url:
            messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ tên session và URL!")
            return

        # Validate URL
        if not (url.startswith('http://') or url.startswith('https://')):
            messagebox.showerror("Lỗi", "URL phải bắt đầu bằng http:// hoặc https://")
            return

        session = {
            'id': len(self.sessions) + 1,
            'name': name,
            'url': url,
            'game_type': game_type,
            'browser': browser,
            'status': 'Chưa kết nối',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'last_connected': None,
            'total_spins': 0,
            'total_scatters': 0
        }

        self.sessions.append(session)
        self.save_sessions()
        self.refresh_sessions()

        # Clear form
        self.session_name_entry.delete(0, 'end')
        self.session_url_entry.delete(0, 'end')

        messagebox.showinfo("Thành công", f"Đã thêm session '{name}'!")

    def connect_session(self):
        """Connect to selected session"""
        selected = self.get_selected_session()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn session để kết nối!")
            return

        try:
            # Update session status
            selected['status'] = 'Đang kết nối...'
            selected['last_connected'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.refresh_sessions()

            # Start connection in separate thread
            connection_thread = threading.Thread(
                target=self.connect_to_session,
                args=(selected,),
                daemon=True
            )
            connection_thread.start()

            self.session_status.insert("end", f"🔗 Đang kết nối đến {selected['name']}...\n")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể kết nối: {e}")

    def connect_to_session(self, session):
        """Connect to session in background thread"""
        try:
            # Initialize scraper for this session
            if not hasattr(self, 'session_scrapers'):
                self.session_scrapers = {}

            scraper = GameScraper()
            success = scraper.navigate_to_game(session['url'])

            if success:
                session['status'] = 'Đã kết nối'
                self.session_scrapers[session['id']] = scraper
                self.active_session = session

                # Start monitoring with game type
                scraper.start_monitoring(
                    session['url'],
                    callback_func=self.on_game_result,
                    interval=1.0,
                    game_type=session['game_type'].lower()
                )

                self.session_status.insert("end", f"✅ Đã kết nối thành công đến {session['name']}\n")
                self.session_status.insert("end", f"🎮 Đang theo dõi game: {session['game_type']}\n")

            else:
                session['status'] = 'Lỗi kết nối'
                self.session_status.insert("end", f"❌ Không thể kết nối đến {session['name']}\n")

        except Exception as e:
            session['status'] = 'Lỗi'
            self.session_status.insert("end", f"❌ Lỗi kết nối: {e}\n")

        # Update UI
        self.refresh_sessions()

    def on_game_result(self, result_data):
        """Handle game result from scraper"""
        if not self.active_session:
            return

        try:
            is_scatter = result_data.get('is_scatter', False)
            timestamp = result_data.get('timestamp', datetime.now())

            # Update session stats
            self.active_session['total_spins'] += 1
            if is_scatter:
                self.active_session['total_scatters'] += 1

            # Update main app
            if is_scatter:
                self.record_scatter()
                self.session_status.insert("end", f"⭐ {timestamp.strftime('%H:%M:%S')} - SCATTER!\n")
            else:
                self.record_normal_spin()
                self.session_status.insert("end", f"🎰 {timestamp.strftime('%H:%M:%S')} - Spin thường\n")

            # Auto-scroll status
            self.session_status.see("end")

            # Save session data
            self.save_sessions()

        except Exception as e:
            print(f"Error handling game result: {e}")

    def pause_session(self):
        """Pause active session"""
        if not self.active_session:
            messagebox.showwarning("Cảnh báo", "Không có session nào đang hoạt động!")
            return

        try:
            session_id = self.active_session['id']
            if hasattr(self, 'session_scrapers') and session_id in self.session_scrapers:
                self.session_scrapers[session_id].stop_monitoring()
                del self.session_scrapers[session_id]

            self.active_session['status'] = 'Tạm dừng'
            self.active_session = None

            self.refresh_sessions()
            self.session_status.insert("end", "⏸️ Đã tạm dừng session\n")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tạm dừng: {e}")

    def delete_session(self):
        """Delete selected session"""
        selected = self.get_selected_session()
        if not selected:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn session để xóa!")
            return

        if messagebox.askyesno("Xác nhận", f"Bạn có chắc muốn xóa session '{selected['name']}'?"):
            # Stop if active
            if self.active_session and self.active_session['id'] == selected['id']:
                self.pause_session()

            # Remove from list
            self.sessions = [s for s in self.sessions if s['id'] != selected['id']]
            self.save_sessions()
            self.refresh_sessions()

            messagebox.showinfo("Thành công", f"Đã xóa session '{selected['name']}'!")

    def get_selected_session(self):
        """Get currently selected session"""
        # This would need to be implemented based on the UI selection
        # For now, return the first session if any
        return self.sessions[0] if self.sessions else None

    def refresh_sessions(self):
        """Refresh session list display"""
        # Clear current list
        for widget in self.session_listbox.winfo_children():
            widget.destroy()

        if not self.sessions:
            ctk.CTkLabel(self.session_listbox, text="Chưa có session nào").pack(pady=10)
            return

        for session in self.sessions:
            session_frame = ctk.CTkFrame(self.session_listbox)
            session_frame.pack(fill="x", padx=5, pady=2)

            # Session info
            info_text = f"🎮 {session['name']}\n"
            info_text += f"🌐 {session['url'][:50]}{'...' if len(session['url']) > 50 else ''}\n"
            info_text += f"📊 {session['total_spins']} spins, {session['total_scatters']} scatters\n"
            info_text += f"📅 {session['created_at']} | 🔗 {session['status']}"

            session_label = ctk.CTkLabel(session_frame, text=info_text, justify="left")
            session_label.pack(side="left", fill="x", expand=True, padx=10, pady=5)

            # Status indicator
            status_color = {
                'Đã kết nối': 'green',
                'Đang kết nối...': 'orange',
                'Tạm dừng': 'gray',
                'Lỗi': 'red',
                'Chưa kết nối': 'blue'
            }.get(session['status'], 'gray')

            status_indicator = ctk.CTkLabel(session_frame, text="●",
                                          text_color=status_color, font=ctk.CTkFont(size=20))
            status_indicator.pack(side="right", padx=10)

    def save_sessions(self):
        """Save sessions to file"""
        try:
            with open('sessions.json', 'w', encoding='utf-8') as f:
                json.dump(self.sessions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving sessions: {e}")

    def load_sessions(self):
        """Load sessions from file"""
        try:
            if os.path.exists('sessions.json'):
                with open('sessions.json', 'r', encoding='utf-8') as f:
                    self.sessions = json.load(f)
            else:
                self.sessions = []
        except Exception as e:
            print(f"Error loading sessions: {e}")
            self.sessions = []

    def update_detailed_stats(self):
        """Update detailed statistics display"""
        # Clear existing stats
        for widget in self.detailed_stats_frame.winfo_children():
            widget.destroy()

        # Get comprehensive stats
        stats = self.db_manager.get_current_stats()
        streak_analysis = self.predictor.get_streak_analysis()

        # Display detailed statistics
        stats_text = f"""
📊 THỐNG KÊ TỔNG QUAN:
• Tổng số spin: {stats['total_spins']}
• Tổng scatter: {stats['total_scatters']}
• Tỷ lệ thực tế: {stats['actual_rate']:.2f}%
• Streak hiện tại: {stats['current_streak']}
• Streak dài nhất: {stats['longest_streak']}
• Trung bình spin/scatter: {stats['avg_spins_per_scatter']:.1f}

🔍 PHÂN TÍCH STREAK:
"""

        if isinstance(streak_analysis, dict) and 'average_streak' in streak_analysis:
            stats_text += f"""• Streak trung bình: {streak_analysis['average_streak']:.1f}
• Streak phổ biến nhất: {streak_analysis['most_common_streak']}
• Tổng lần ra scatter: {streak_analysis['total_scatters']}
"""
        else:
            stats_text += "• Chưa có đủ dữ liệu để phân tích\n"

        # Display in text widget
        stats_display = ctk.CTkTextbox(self.detailed_stats_frame, height=200)
        stats_display.pack(fill="x", padx=10, pady=5)
        stats_display.insert("1.0", stats_text)

    def export_data(self):
        """Export data to file"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                self.db_manager.export_data(filename)
                messagebox.showinfo("Thành công", f"Đã xuất dữ liệu ra {filename}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể xuất dữ liệu: {e}")

    def import_data(self):
        """Import data from file"""
        try:
            from tkinter import filedialog
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if filename:
                self.db_manager.import_data(filename)
                self.load_data()
                self.update_stats()
                messagebox.showinfo("Thành công", f"Đã nhập dữ liệu từ {filename}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể nhập dữ liệu: {e}")

    def show_about(self):
        """Show about dialog"""
        about_text = """
🎰 PGGame Scatter Predictor v1.0

Tool dự đoán tỉ lệ ra scatter cho game slot PGGame

Tính năng:
• Theo dõi và phân tích kết quả spin
• Dự đoán xác suất ra scatter
• Thống kê chi tiết và biểu đồ
• Chế độ tự động (thử nghiệm)
• Xuất/nhập dữ liệu

⚠️ Lưu ý: Tool chỉ mang tính tham khảo
Chơi có trách nhiệm!

Phát triển bởi: AI Assistant
"""
        messagebox.showinfo("Về chương trình", about_text)

    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Add menu bar
        self.setup_menu()

        self.root.mainloop()

    def setup_menu(self):
        """Setup menu bar"""
        try:
            menubar = tk.Menu(self.root)
            self.root.config(menu=menubar)

            # File menu
            file_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Tệp", menu=file_menu)
            file_menu.add_command(label="Xuất dữ liệu...", command=self.export_data)
            file_menu.add_command(label="Nhập dữ liệu...", command=self.import_data)
            file_menu.add_separator()
            file_menu.add_command(label="Thoát", command=self.on_closing)

            # Tools menu
            tools_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Công cụ", menu=tools_menu)
            tools_menu.add_command(label="Reset dữ liệu", command=self.reset_data)
            tools_menu.add_command(label="Cập nhật thống kê", command=self.update_detailed_stats)

            # Help menu
            help_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Trợ giúp", menu=help_menu)
            help_menu.add_command(label="Về chương trình", command=self.show_about)

        except Exception as e:
            print(f"Could not create menu: {e}")

    def on_closing(self):
        """Handle application closing"""
        self.running = False
        if self.auto_update_thread:
            self.auto_update_thread.join()

        # Cleanup scraper
        if hasattr(self.scraper, 'cleanup'):
            self.scraper.cleanup()

        self.root.destroy()

if __name__ == "__main__":
    app = PGGameScatterTool()
    app.run()
