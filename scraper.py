#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Game Scraper for PGGame Scatter Predictor
Module tự động đọc dữ liệu từ website game
"""

import time
import json
import re
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import threading

try:
    import requests
    from bs4 import BeautifulSoup
except ImportError:
    print("Warning: requests and beautifulsoup4 not available")

try:
    import undetected_chromedriver as uc
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    print("Warning: Selenium not available")
    SELENIUM_AVAILABLE = False

try:
    import pyautogui
    from PIL import Image, ImageGrab
    import cv2
    import numpy as np
    IMAGE_PROCESSING_AVAILABLE = True
except ImportError:
    print("Warning: Image processing libraries not available")
    IMAGE_PROCESSING_AVAILABLE = False

class GameScraper:
    """Scrapes game data from PGGame websites"""
    
    def __init__(self):
        self.driver = None
        self.is_running = False
        self.last_result = None
        self.session_data = []
        self.callbacks = []
        
        # Game detection patterns
        self.scatter_patterns = {
            'pggame': {
                'scatter_symbols': ['scatter', 'bonus', 'free', 'freespin'],
                'win_indicators': ['win', 'congratulations', 'bonus', 'big win', 'mega win'],
                'spin_button': ['spin', 'play', 'start', 'auto'],
                'balance_selector': '.balance, .money, .credit, .coins',
                'result_selector': '.result, .win, .outcome, .total-win',
                'game_canvas': 'canvas, #game-canvas, .game-area',
                'scatter_class': '.scatter, .bonus-symbol, .free-spin'
            },
            'pragmatic': {
                'scatter_symbols': ['scatter', 'bonus', 'free', 'freespins'],
                'win_indicators': ['win', 'bonus', 'freespins', 'feature'],
                'spin_button': ['spin', 'play', 'start'],
                'balance_selector': '.balance, .credit',
                'result_selector': '.win-amount, .total-win',
                'game_canvas': 'canvas, .game-container',
                'scatter_class': '.scatter, .bonus'
            },
            'evolution': {
                'scatter_symbols': ['scatter', 'bonus', 'multiplier'],
                'win_indicators': ['win', 'bonus', 'multiplier'],
                'spin_button': ['spin', 'bet'],
                'balance_selector': '.balance, .wallet',
                'result_selector': '.win, .payout',
                'game_canvas': 'canvas, .game-area',
                'scatter_class': '.scatter, .special'
            },
            'mahjong': {
                'scatter_symbols': ['scatter', 'bonus', 'free', 'dragon', 'phoenix'],
                'win_indicators': ['win', 'bonus', 'free', 'big win'],
                'spin_button': ['spin', 'play', 'auto'],
                'balance_selector': '.balance, .credit, .coins',
                'result_selector': '.win, .total-win, .payout',
                'game_canvas': 'canvas, .game-board, .mahjong-board',
                'scatter_class': '.scatter, .bonus, .dragon, .phoenix'
            }
        }
        
        # Image recognition templates
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """Load image templates for pattern recognition"""
        template_files = [
            'scatter_symbol.png',
            'bonus_symbol.png',
            'spin_button.png',
            'win_popup.png'
        ]
        
        for template_file in template_files:
            try:
                template_path = f"assets/templates/{template_file}"
                if os.path.exists(template_path):
                    self.templates[template_file] = cv2.imread(template_path, 0)
            except Exception as e:
                print(f"Could not load template {template_file}: {e}")
    
    def setup_driver(self, headless: bool = False) -> bool:
        """Setup Chrome driver for web scraping"""
        try:
            if not SELENIUM_AVAILABLE:
                print("❌ Selenium không có sẵn")
                return False

            print("🔧 Đang thiết lập Chrome WebDriver...")

            options = Options()

            if headless:
                options.add_argument('--headless')

            # Thêm các options để tránh bị phát hiện
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')  # Tăng tốc load
            # Tạm thời bỏ disable-javascript để game có thể hoạt động
            # options.add_argument('--disable-javascript')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # User agent để giống trình duyệt thật
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # Thử sử dụng undetected chrome driver
            try:
                self.driver = uc.Chrome(options=options)
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            except Exception as uc_error:
                print(f"⚠️ Undetected Chrome failed: {uc_error}")
                print("🔄 Thử sử dụng Chrome driver thông thường...")

                # Fallback to regular Chrome driver
                from selenium import webdriver
                from webdriver_manager.chrome import ChromeDriverManager
                from selenium.webdriver.chrome.service import Service

                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)

            print("✅ WebDriver đã sẵn sàng")
            return True

        except Exception as e:
            print(f"❌ Lỗi thiết lập WebDriver: {e}")
            print("💡 Thử cài đặt Chrome hoặc cập nhật Chrome lên phiên bản mới nhất")
            return False
    
    def navigate_to_game(self, url: str) -> bool:
        """Navigate to game URL"""
        try:
            if not SELENIUM_AVAILABLE:
                print("❌ Selenium không có sẵn. Cài đặt: pip install selenium")
                return False

            if not self.driver:
                print("🔧 Đang thiết lập WebDriver...")
                if not self.setup_driver():
                    print("❌ Không thể thiết lập WebDriver")
                    return False

            print(f"🌐 Đang điều hướng đến: {url}")
            self.driver.get(url)

            print("⏳ Đang đợi game load...")
            time.sleep(5)

            # Kiểm tra trang đã load thành công
            try:
                # Đợi body element load
                WebDriverWait(self.driver, 15).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                print("✅ Trang web đã load")
            except:
                print("⚠️ Trang web load chậm, tiếp tục...")

            # Thử tìm game elements với nhiều selector khác nhau
            selectors_to_try = [
                "canvas",
                "iframe",
                "#game-canvas",
                ".game-area",
                ".game-container",
                ".game-frame",
                "[id*='game']",
                "[class*='game']",
                "[src*='game']"
            ]

            game_found = False
            for selector in selectors_to_try:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✅ Tìm thấy game element: {selector} ({len(elements)} elements)")
                        game_found = True
                        break
                except:
                    continue

            if not game_found:
                print("⚠️ Không tìm thấy game elements cụ thể")
                print("🔍 Đang kiểm tra nội dung trang...")

                # Kiểm tra title và URL hiện tại
                current_url = self.driver.current_url
                title = self.driver.title
                print(f"📄 Title: {title}")
                print(f"🔗 Current URL: {current_url}")

                # Kiểm tra có iframe không (game thường được embed trong iframe)
                iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
                if iframes:
                    print(f"🖼️ Tìm thấy {len(iframes)} iframe(s)")
                    # Thử switch vào iframe đầu tiên
                    try:
                        self.driver.switch_to.frame(iframes[0])
                        print("🔄 Đã switch vào iframe")
                        time.sleep(3)

                        # Tìm game trong iframe
                        for selector in selectors_to_try:
                            try:
                                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                if elements:
                                    print(f"✅ Tìm thấy game trong iframe: {selector}")
                                    game_found = True
                                    break
                            except:
                                continue

                        if not game_found:
                            self.driver.switch_to.default_content()
                    except Exception as e:
                        print(f"⚠️ Không thể switch vào iframe: {e}")
                        self.driver.switch_to.default_content()

            # Đợi thêm thời gian cho game load hoàn toàn
            print("⏳ Đang đợi game load hoàn toàn...")
            time.sleep(10)

            print("✅ Điều hướng hoàn tất")
            return True

        except Exception as e:
            print(f"❌ Lỗi điều hướng: {e}")
            print(f"🔗 URL: {url}")

            # Thử lấy thêm thông tin debug
            try:
                if hasattr(self, 'driver') and self.driver:
                    current_url = self.driver.current_url
                    title = self.driver.title
                    print(f"📄 Current Title: {title}")
                    print(f"🔗 Current URL: {current_url}")
            except:
                pass

            return False
    
    def detect_game_type(self, url: str) -> str:
        """Auto-detect game type from URL"""
        url_lower = url.lower()

        if 'pg' in url_lower or 'pocket' in url_lower:
            return 'pggame'
        elif 'pragmatic' in url_lower or 'pp' in url_lower:
            return 'pragmatic'
        elif 'evolution' in url_lower or 'evo' in url_lower:
            return 'evolution'
        elif 'mahjong' in url_lower or 'mah' in url_lower:
            return 'mahjong'
        else:
            return 'pggame'  # Default

    def set_game_type(self, game_type: str):
        """Set current game type for detection"""
        self.current_game_type = game_type.lower()

    def detect_scatter_result(self, game_type: str = None) -> Optional[bool]:
        """Detect if scatter occurred in the current spin"""
        try:
            if not game_type:
                game_type = getattr(self, 'current_game_type', 'pggame')

            # Method 1: Check DOM elements
            scatter_detected = self.check_dom_for_scatter(game_type)
            if scatter_detected is not None:
                return scatter_detected

            # Method 2: Image recognition
            scatter_detected = self.check_screen_for_scatter()
            if scatter_detected is not None:
                return scatter_detected

            # Method 3: Text analysis
            scatter_detected = self.check_text_for_scatter(game_type)
            return scatter_detected

        except Exception as e:
            print(f"Error detecting scatter: {e}")
            return None
    
    def check_dom_for_scatter(self, game_type: str = 'pggame') -> Optional[bool]:
        """Check DOM elements for scatter indicators"""
        try:
            if not SELENIUM_AVAILABLE:
                return None

            patterns = self.scatter_patterns.get(game_type, self.scatter_patterns['pggame'])

            # Look for scatter symbols using game-specific selectors
            scatter_selectors = [
                patterns['scatter_class'],
                '.scatter', '.bonus', '.free-spin',
                '[class*="scatter"]', '[class*="bonus"]',
                '[id*="scatter"]', '[id*="bonus"]'
            ]

            for selector in scatter_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            return True
                except:
                    continue

            # Check for win amount indicators
            win_selectors = patterns['result_selector'].split(', ') + [
                '.win-amount', '.total-win', '.bonus-win',
                '[class*="win"]', '[class*="bonus"]'
            ]

            for selector in win_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text:
                            # Check if win amount is significant (might indicate scatter)
                            text = element.text.lower()
                            if any(keyword in text for keyword in patterns['scatter_symbols']):
                                return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"Error checking DOM: {e}")
            return None
    
    def check_screen_for_scatter(self) -> Optional[bool]:
        """Use image recognition to detect scatter on screen"""
        try:
            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)
            
            # Check against templates
            for template_name, template in self.templates.items():
                if 'scatter' in template_name or 'bonus' in template_name:
                    result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
                    _, max_val, _, _ = cv2.minMaxLoc(result)
                    
                    if max_val > 0.8:  # High confidence threshold
                        return True
            
            return False
            
        except Exception as e:
            print(f"Error in image recognition: {e}")
            return None
    
    def check_text_for_scatter(self, game_type: str = 'pggame') -> Optional[bool]:
        """Check page text for scatter indicators"""
        try:
            if not SELENIUM_AVAILABLE:
                return None

            page_text = self.driver.page_source.lower()
            patterns = self.scatter_patterns.get(game_type, self.scatter_patterns['pggame'])

            # Use game-specific keywords
            scatter_keywords = patterns['scatter_symbols'] + patterns['win_indicators']

            for keyword in scatter_keywords:
                if keyword in page_text:
                    return True

            return False

        except Exception as e:
            print(f"Error checking text: {e}")
            return None
    
    def monitor_game(self, callback_func=None, interval: float = 1.0, game_type: str = 'pggame'):
        """Monitor game for scatter results"""
        self.is_running = True
        self.current_game_type = game_type

        if callback_func:
            self.callbacks.append(callback_func)

        print(f"🎮 Bắt đầu theo dõi game type: {game_type}")

        while self.is_running:
            try:
                # Detect current game state
                scatter_result = self.detect_scatter_result(game_type)

                if scatter_result is not None and scatter_result != self.last_result:
                    # New result detected
                    result_data = {
                        'timestamp': datetime.now(),
                        'is_scatter': scatter_result,
                        'confidence': 0.8,  # Default confidence
                        'game_type': game_type,
                        'detection_method': 'auto'
                    }

                    self.session_data.append(result_data)
                    self.last_result = scatter_result

                    print(f"🎯 Phát hiện: {'SCATTER' if scatter_result else 'SPIN THƯỜNG'} - {game_type}")

                    # Notify callbacks
                    for callback in self.callbacks:
                        try:
                            callback(result_data)
                        except Exception as e:
                            print(f"Callback error: {e}")

                time.sleep(interval)

            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def start_monitoring(self, url: str, callback_func=None, interval: float = 1.0, game_type: str = None):
        """Start monitoring in a separate thread"""
        if not game_type:
            game_type = self.detect_game_type(url)

        if self.navigate_to_game(url):
            monitor_thread = threading.Thread(
                target=self.monitor_game,
                args=(callback_func, interval, game_type),
                daemon=True
            )
            monitor_thread.start()
            return True
        return False
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_running = False
    
    def get_game_info(self) -> Dict:
        """Get current game information"""
        try:
            info = {
                'url': self.driver.current_url if self.driver else None,
                'title': self.driver.title if self.driver else None,
                'session_duration': len(self.session_data),
                'last_result': self.last_result,
                'total_scatters': sum(1 for r in self.session_data if r['is_scatter']),
                'total_spins': len(self.session_data)
            }
            return info
        except Exception as e:
            print(f"Error getting game info: {e}")
            return {}
    
    def simulate_spin(self) -> bool:
        """Simulate a spin button click (use with caution)"""
        try:
            # Look for spin button
            spin_selectors = [
                '.spin-button', '.play-button', '.start-button',
                '[class*="spin"]', '[class*="play"]',
                'button[onclick*="spin"]', 'button[onclick*="play"]'
            ]
            
            for selector in spin_selectors:
                try:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if button.is_displayed() and button.is_enabled():
                        button.click()
                        return True
                except NoSuchElementException:
                    continue
            
            return False
            
        except Exception as e:
            print(f"Error simulating spin: {e}")
            return False
    
    def get_balance(self) -> Optional[float]:
        """Get current balance from game"""
        try:
            balance_selectors = [
                '.balance', '.money', '.credit', '.coins',
                '[class*="balance"]', '[class*="money"]',
                '[id*="balance"]', '[id*="money"]'
            ]
            
            for selector in balance_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        text = element.text
                        # Extract numeric value
                        numbers = re.findall(r'[\d,]+\.?\d*', text)
                        if numbers:
                            return float(numbers[0].replace(',', ''))
                except NoSuchElementException:
                    continue
            
            return None
            
        except Exception as e:
            print(f"Error getting balance: {e}")
            return None
    
    def take_screenshot(self, filename: str = None) -> str:
        """Take screenshot of current game state"""
        try:
            if not filename:
                filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            
            self.driver.save_screenshot(filename)
            return filename
            
        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return None
    
    def export_session_data(self, filepath: str):
        """Export current session data"""
        try:
            export_data = {
                'session_info': {
                    'start_time': self.session_data[0]['timestamp'].isoformat() if self.session_data else None,
                    'end_time': datetime.now().isoformat(),
                    'total_spins': len(self.session_data),
                    'total_scatters': sum(1 for r in self.session_data if r['is_scatter'])
                },
                'results': [
                    {
                        'timestamp': r['timestamp'].isoformat(),
                        'is_scatter': r['is_scatter'],
                        'confidence': r['confidence']
                    }
                    for r in self.session_data
                ]
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error exporting session data: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources"""
        self.stop_monitoring()
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                print(f"Error closing driver: {e}")
        self.driver = None
    
    def __del__(self):
        """Destructor"""
        self.cleanup()
