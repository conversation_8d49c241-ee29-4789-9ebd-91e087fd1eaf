#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Game Scraper for PGGame Scatter Predictor
Module tự động đọc dữ liệu từ website game
"""

import time
import json
import re
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import threading

try:
    import requests
    from bs4 import BeautifulSoup
except ImportError:
    print("Warning: requests and beautifulsoup4 not available")

try:
    import undetected_chromedriver as uc
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    print("Warning: Selenium not available")
    SELENIUM_AVAILABLE = False

try:
    import pyautogui
    from PIL import Image, ImageGrab
    import cv2
    import numpy as np
    IMAGE_PROCESSING_AVAILABLE = True
except ImportError:
    print("Warning: Image processing libraries not available")
    IMAGE_PROCESSING_AVAILABLE = False

class GameScraper:
    """Scrapes game data from PGGame websites"""
    
    def __init__(self):
        self.driver = None
        self.is_running = False
        self.last_result = None
        self.session_data = []
        self.callbacks = []
        
        # Game detection patterns
        self.scatter_patterns = {
            'pggame': {
                'scatter_symbols': ['scatter', 'bonus', 'free'],
                'win_indicators': ['win', 'congratulations', 'bonus'],
                'spin_button': ['spin', 'play', 'start'],
                'balance_selector': '.balance, .money, .credit',
                'result_selector': '.result, .win, .outcome'
            }
        }
        
        # Image recognition templates
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """Load image templates for pattern recognition"""
        template_files = [
            'scatter_symbol.png',
            'bonus_symbol.png',
            'spin_button.png',
            'win_popup.png'
        ]
        
        for template_file in template_files:
            try:
                template_path = f"assets/templates/{template_file}"
                if os.path.exists(template_path):
                    self.templates[template_file] = cv2.imread(template_path, 0)
            except Exception as e:
                print(f"Could not load template {template_file}: {e}")
    
    def setup_driver(self, headless: bool = False) -> bool:
        """Setup Chrome driver for web scraping"""
        try:
            options = Options()
            
            if headless:
                options.add_argument('--headless')
            
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Use undetected chrome driver to avoid detection
            self.driver = uc.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
            
        except Exception as e:
            print(f"Error setting up driver: {e}")
            return False
    
    def navigate_to_game(self, url: str) -> bool:
        """Navigate to game URL"""
        try:
            if not self.driver:
                if not self.setup_driver():
                    return False
            
            self.driver.get(url)
            time.sleep(3)
            
            # Wait for game to load
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "canvas"))
            )
            
            return True
            
        except Exception as e:
            print(f"Error navigating to game: {e}")
            return False
    
    def detect_scatter_result(self) -> Optional[bool]:
        """Detect if scatter occurred in the current spin"""
        try:
            # Method 1: Check DOM elements
            scatter_detected = self.check_dom_for_scatter()
            if scatter_detected is not None:
                return scatter_detected
            
            # Method 2: Image recognition
            scatter_detected = self.check_screen_for_scatter()
            if scatter_detected is not None:
                return scatter_detected
            
            # Method 3: Text analysis
            scatter_detected = self.check_text_for_scatter()
            return scatter_detected
            
        except Exception as e:
            print(f"Error detecting scatter: {e}")
            return None
    
    def check_dom_for_scatter(self) -> Optional[bool]:
        """Check DOM elements for scatter indicators"""
        try:
            # Look for scatter symbols in common selectors
            selectors = [
                '.scatter', '.bonus', '.free-spin',
                '[class*="scatter"]', '[class*="bonus"]',
                '[id*="scatter"]', '[id*="bonus"]'
            ]
            
            for selector in selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        if element.is_displayed():
                            return True
            
            # Check for win amount indicators
            win_selectors = [
                '.win-amount', '.total-win', '.bonus-win',
                '[class*="win"]', '[class*="bonus"]'
            ]
            
            for selector in win_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.text:
                        # Check if win amount is significant (might indicate scatter)
                        text = element.text.lower()
                        if any(keyword in text for keyword in ['bonus', 'free', 'scatter']):
                            return True
            
            return False
            
        except Exception as e:
            print(f"Error checking DOM: {e}")
            return None
    
    def check_screen_for_scatter(self) -> Optional[bool]:
        """Use image recognition to detect scatter on screen"""
        try:
            # Take screenshot
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)
            
            # Check against templates
            for template_name, template in self.templates.items():
                if 'scatter' in template_name or 'bonus' in template_name:
                    result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
                    _, max_val, _, _ = cv2.minMaxLoc(result)
                    
                    if max_val > 0.8:  # High confidence threshold
                        return True
            
            return False
            
        except Exception as e:
            print(f"Error in image recognition: {e}")
            return None
    
    def check_text_for_scatter(self) -> Optional[bool]:
        """Check page text for scatter indicators"""
        try:
            page_text = self.driver.page_source.lower()
            
            scatter_keywords = [
                'scatter', 'bonus', 'free spin', 'free game',
                'congratulations', 'big win', 'mega win'
            ]
            
            for keyword in scatter_keywords:
                if keyword in page_text:
                    return True
            
            return False
            
        except Exception as e:
            print(f"Error checking text: {e}")
            return None
    
    def monitor_game(self, callback_func=None, interval: float = 1.0):
        """Monitor game for scatter results"""
        self.is_running = True
        
        if callback_func:
            self.callbacks.append(callback_func)
        
        while self.is_running:
            try:
                # Detect current game state
                scatter_result = self.detect_scatter_result()
                
                if scatter_result is not None and scatter_result != self.last_result:
                    # New result detected
                    result_data = {
                        'timestamp': datetime.now(),
                        'is_scatter': scatter_result,
                        'confidence': 0.8  # Default confidence
                    }
                    
                    self.session_data.append(result_data)
                    self.last_result = scatter_result
                    
                    # Notify callbacks
                    for callback in self.callbacks:
                        try:
                            callback(result_data)
                        except Exception as e:
                            print(f"Callback error: {e}")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def start_monitoring(self, url: str, callback_func=None, interval: float = 1.0):
        """Start monitoring in a separate thread"""
        if self.navigate_to_game(url):
            monitor_thread = threading.Thread(
                target=self.monitor_game,
                args=(callback_func, interval),
                daemon=True
            )
            monitor_thread.start()
            return True
        return False
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_running = False
    
    def get_game_info(self) -> Dict:
        """Get current game information"""
        try:
            info = {
                'url': self.driver.current_url if self.driver else None,
                'title': self.driver.title if self.driver else None,
                'session_duration': len(self.session_data),
                'last_result': self.last_result,
                'total_scatters': sum(1 for r in self.session_data if r['is_scatter']),
                'total_spins': len(self.session_data)
            }
            return info
        except Exception as e:
            print(f"Error getting game info: {e}")
            return {}
    
    def simulate_spin(self) -> bool:
        """Simulate a spin button click (use with caution)"""
        try:
            # Look for spin button
            spin_selectors = [
                '.spin-button', '.play-button', '.start-button',
                '[class*="spin"]', '[class*="play"]',
                'button[onclick*="spin"]', 'button[onclick*="play"]'
            ]
            
            for selector in spin_selectors:
                try:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if button.is_displayed() and button.is_enabled():
                        button.click()
                        return True
                except NoSuchElementException:
                    continue
            
            return False
            
        except Exception as e:
            print(f"Error simulating spin: {e}")
            return False
    
    def get_balance(self) -> Optional[float]:
        """Get current balance from game"""
        try:
            balance_selectors = [
                '.balance', '.money', '.credit', '.coins',
                '[class*="balance"]', '[class*="money"]',
                '[id*="balance"]', '[id*="money"]'
            ]
            
            for selector in balance_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        text = element.text
                        # Extract numeric value
                        numbers = re.findall(r'[\d,]+\.?\d*', text)
                        if numbers:
                            return float(numbers[0].replace(',', ''))
                except NoSuchElementException:
                    continue
            
            return None
            
        except Exception as e:
            print(f"Error getting balance: {e}")
            return None
    
    def take_screenshot(self, filename: str = None) -> str:
        """Take screenshot of current game state"""
        try:
            if not filename:
                filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            
            self.driver.save_screenshot(filename)
            return filename
            
        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return None
    
    def export_session_data(self, filepath: str):
        """Export current session data"""
        try:
            export_data = {
                'session_info': {
                    'start_time': self.session_data[0]['timestamp'].isoformat() if self.session_data else None,
                    'end_time': datetime.now().isoformat(),
                    'total_spins': len(self.session_data),
                    'total_scatters': sum(1 for r in self.session_data if r['is_scatter'])
                },
                'results': [
                    {
                        'timestamp': r['timestamp'].isoformat(),
                        'is_scatter': r['is_scatter'],
                        'confidence': r['confidence']
                    }
                    for r in self.session_data
                ]
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error exporting session data: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources"""
        self.stop_monitoring()
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                print(f"Error closing driver: {e}")
        self.driver = None
    
    def __del__(self):
        """Destructor"""
        self.cleanup()
