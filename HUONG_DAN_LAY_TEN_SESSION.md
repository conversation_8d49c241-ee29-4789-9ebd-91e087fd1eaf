# 📝 Hướng dẫn lấy tên Session - PGGame Scatter Predictor

## 🎯 Tổng quan

Trong ứng dụng PGGame Scatter Predictor, có nhiều cách để lấy tên session tùy thuộc vào mục đích sử dụng. Tài liệu này sẽ hướng dẫn chi tiết các phương pháp.

## 🔧 Các phương thức có sẵn

### 1. **`get_active_session_name()`**
Lấy tên session đang hoạt động (đang kết nối)

```python
# Sử dụng
active_name = app.get_active_session_name()
print(f"Session đang chơi: {active_name}")

# Kết quả
# Session đang chơi: Mạt Chược 2 - Table 1
```

**Khi nào sử dụng:**
- Hiển thị session đang chơi trên giao diện
- Ghi log hoạt động hiện tại
- Kiểm tra xem có session nào đang hoạt động không

### 2. **`get_session_name_by_id(session_id)`**
Lấy tên session theo ID

```python
# Sử dụng
name = app.get_session_name_by_id(1)
print(f"Session ID 1: {name}")

# Kết quả
# Session ID 1: Mạt Chược 2 - Table 1
```

**Khi nào sử dụng:**
- Khi bạn có ID session và cần tên
- Xử lý callback từ database
- Tìm kiếm session cụ thể

### 3. **`get_all_session_names()`**
Lấy danh sách tất cả tên session

```python
# Sử dụng
all_names = app.get_all_session_names()
for i, name in enumerate(all_names, 1):
    print(f"{i}. {name}")

# Kết quả
# 1. Mạt Chược 2 - Table 1
# 2. Fortune Tiger - VIP
# 3. Sweet Bonanza - Demo
```

**Khi nào sử dụng:**
- Tạo dropdown menu chọn session
- Hiển thị danh sách session
- Export danh sách session

### 4. **`find_session_by_name(name)`**
Tìm session theo tên (trả về object session đầy đủ)

```python
# Sử dụng
session = app.find_session_by_name("Mạt Chược 2 - Table 1")
if session:
    print(f"Tìm thấy session: {session['name']}")
    print(f"URL: {session['url']}")
    print(f"Trạng thái: {session['status']}")

# Kết quả
# Tìm thấy session: Mạt Chược 2 - Table 1
# URL: https://game.example.com/mahjong-ways-2
# Trạng thái: Đã kết nối
```

**Khi nào sử dụng:**
- Cần thông tin chi tiết của session
- Kiểm tra session có tồn tại không
- Cập nhật thông tin session

### 5. **`get_session_info(session_name=None)`**
Lấy thông tin chi tiết session

```python
# Lấy info session đang hoạt động
info = app.get_session_info()

# Hoặc lấy info session theo tên
info = app.get_session_info("Fortune Tiger - VIP")

if info:
    print(f"Tên: {info['name']}")
    print(f"Game: {info['game_type']}")
    print(f"Spins: {info['total_spins']}")
    print(f"Scatters: {info['total_scatters']}")
```

**Khi nào sử dụng:**
- Hiển thị thống kê session
- Tạo báo cáo chi tiết
- Kiểm tra hiệu suất session

## 🎮 Ví dụ sử dụng thực tế

### **Scenario 1: Hiển thị session đang chơi**

```python
def update_current_session_display():
    """Cập nhật hiển thị session hiện tại"""
    active_name = app.get_active_session_name()
    
    if active_name:
        # Hiển thị tên session
        session_label.configure(text=f"🎮 Đang chơi: {active_name}")
        
        # Lấy thêm thông tin
        info = app.get_session_info()
        if info and info['total_spins'] > 0:
            rate = info['total_scatters'] / info['total_spins'] * 100
            stats_label.configure(
                text=f"📊 {info['total_spins']} spins | ⭐ {info['total_scatters']} scatters ({rate:.1f}%)"
            )
    else:
        session_label.configure(text="❌ Chưa kết nối session nào")
        stats_label.configure(text="")
```

### **Scenario 2: Menu chọn session**

```python
def create_session_menu():
    """Tạo menu chọn session"""
    all_names = app.get_all_session_names()
    
    if not all_names:
        return ["Chưa có session nào"]
    
    menu_items = []
    for name in all_names:
        session = app.find_session_by_name(name)
        if session:
            status_icon = {
                'Đã kết nối': '🟢',
                'Tạm dừng': '⚫',
                'Chưa kết nối': '🔵',
                'Lỗi': '🔴'
            }.get(session['status'], '⚪')
            
            menu_items.append(f"{status_icon} {name}")
    
    return menu_items
```

### **Scenario 3: Tìm kiếm session**

```python
def search_session(search_term):
    """Tìm kiếm session theo tên"""
    all_names = app.get_all_session_names()
    
    # Tìm kiếm không phân biệt hoa thường
    matches = []
    for name in all_names:
        if search_term.lower() in name.lower():
            matches.append(name)
    
    return matches

# Sử dụng
results = search_session("mạt chược")
print(f"Tìm thấy {len(results)} session:")
for result in results:
    print(f"  - {result}")
```

### **Scenario 4: Thống kê tổng quan**

```python
def get_overall_stats():
    """Lấy thống kê tổng quan tất cả session"""
    all_names = app.get_all_session_names()
    
    total_spins = 0
    total_scatters = 0
    active_count = 0
    
    for name in all_names:
        info = app.get_session_info(name)
        if info:
            total_spins += info['total_spins']
            total_scatters += info['total_scatters']
            if info['status'] == 'Đã kết nối':
                active_count += 1
    
    return {
        'total_sessions': len(all_names),
        'active_sessions': active_count,
        'total_spins': total_spins,
        'total_scatters': total_scatters,
        'overall_rate': (total_scatters / total_spins * 100) if total_spins > 0 else 0
    }
```

## ⚠️ Xử lý lỗi

### **Kiểm tra session tồn tại**

```python
def safe_get_session_name(session_id):
    """Lấy tên session an toàn"""
    try:
        name = app.get_session_name_by_id(session_id)
        return name if name else "Session không tồn tại"
    except Exception as e:
        print(f"Lỗi lấy tên session: {e}")
        return "Lỗi"

def safe_find_session(name):
    """Tìm session an toàn"""
    try:
        session = app.find_session_by_name(name)
        if session:
            return session
        else:
            print(f"Không tìm thấy session: {name}")
            return None
    except Exception as e:
        print(f"Lỗi tìm session: {e}")
        return None
```

### **Kiểm tra active session**

```python
def check_active_session():
    """Kiểm tra session đang hoạt động"""
    active_name = app.get_active_session_name()
    
    if active_name:
        print(f"✅ Session đang hoạt động: {active_name}")
        return True
    else:
        print("❌ Không có session nào đang hoạt động")
        return False
```

## 💡 Tips sử dụng hiệu quả

### **1. Cache tên session**
```python
# Lưu cache để tránh gọi lại nhiều lần
cached_names = app.get_all_session_names()

# Sử dụng cache
for name in cached_names:
    # Xử lý...
    pass
```

### **2. Validate tên session**
```python
def is_valid_session_name(name):
    """Kiểm tra tên session hợp lệ"""
    if not name or len(name.strip()) == 0:
        return False
    
    # Kiểm tra ký tự đặc biệt
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
    for char in invalid_chars:
        if char in name:
            return False
    
    return True
```

### **3. Format tên session**
```python
def format_session_name(name, max_length=30):
    """Format tên session cho hiển thị"""
    if len(name) <= max_length:
        return name
    else:
        return name[:max_length-3] + "..."

# Sử dụng
display_name = format_session_name("Mạt Chược 2 - Table 1 - VIP Room", 20)
print(display_name)  # "Mạt Chược 2 - Tab..."
```

## 🔄 Cập nhật real-time

### **Theo dõi thay đổi session**
```python
def on_session_changed():
    """Callback khi session thay đổi"""
    current_name = app.get_active_session_name()
    
    # Cập nhật UI
    update_session_display(current_name)
    
    # Log thay đổi
    if current_name:
        print(f"🔄 Chuyển sang session: {current_name}")
    else:
        print("🔄 Ngắt kết nối session")

def update_session_display(session_name):
    """Cập nhật hiển thị session"""
    if session_name:
        info = app.get_session_info(session_name)
        if info:
            # Cập nhật các widget UI
            pass
```

## 📞 Troubleshooting

### **Lỗi thường gặp:**

1. **`None` được trả về:**
   - Session không tồn tại
   - Không có session nào đang hoạt động
   - ID session không hợp lệ

2. **Exception khi gọi phương thức:**
   - Kiểm tra app đã khởi tạo chưa
   - Kiểm tra sessions list đã load chưa

3. **Tên session bị trùng:**
   - Validate tên trước khi thêm
   - Sử dụng ID thay vì tên để định danh

### **Debug:**
```python
def debug_sessions():
    """Debug thông tin session"""
    print("🔍 Debug Sessions:")
    print(f"  Tổng số session: {len(app.sessions)}")
    print(f"  Active session: {app.get_active_session_name()}")
    
    for i, session in enumerate(app.sessions):
        print(f"  {i+1}. {session['name']} (ID: {session['id']}, Status: {session['status']})")
```

---

**💡 Lưu ý:** Luôn kiểm tra giá trị trả về trước khi sử dụng để tránh lỗi `None` hoặc `KeyError`.
