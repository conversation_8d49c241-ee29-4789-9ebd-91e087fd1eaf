#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test connection to specific URL
Script test kết nối đến URL cụ thể
"""

import sys
import time
from scraper import GameScraper

def test_url_connection(url):
    """Test connection to specific URL"""
    print("🧪 Test kết nối URL")
    print("=" * 50)
    print(f"🔗 URL: {url}")
    print()
    
    # Tạo scraper instance
    scraper = GameScraper()
    
    try:
        print("🚀 Bắt đầu test kết nối...")
        
        # Test navigate to game
        success = scraper.navigate_to_game(url)
        
        if success:
            print("\n✅ KẾT NỐI THÀNH CÔNG!")
            
            # Lấy thông tin game
            info = scraper.get_game_info()
            print(f"📄 Title: {info.get('title', 'N/A')}")
            print(f"🔗 Current URL: {info.get('url', 'N/A')}")
            
            # Thử chụp screenshot
            print("\n📸 <PERSON>ang chụp screenshot...")
            screenshot_file = scraper.take_screenshot("test_connection.png")
            if screenshot_file:
                print(f"✅ Screenshot saved: {screenshot_file}")
            
            # Test detect game type
            game_type = scraper.detect_game_type(url)
            print(f"🎮 Game type detected: {game_type}")
            
            # Đợi một chút để quan sát
            print("\n⏳ Đang đợi 10 giây để quan sát...")
            time.sleep(10)
            
        else:
            print("\n❌ KẾT NỐI THẤT BẠI!")
            print("💡 Các bước khắc phục:")
            print("   1. Kiểm tra URL có chính xác không")
            print("   2. Thử mở URL trên trình duyệt thủ công")
            print("   3. Kiểm tra kết nối internet")
            print("   4. Đảm bảo Chrome đã được cài đặt")
            
    except Exception as e:
        print(f"\n💥 LỖI KHÔNG MONG MUỐN: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("\n🧹 Đang dọn dẹp...")
        scraper.cleanup()
        print("✅ Hoàn tất!")

def main():
    """Main function"""
    # URL test - thay đổi URL này theo URL của bạn
    test_url = "https://www.13win16.com/home/<USER>"
    
    print("🎰 PGGame Scatter Predictor - Test Connection")
    print("=" * 60)
    
    # Kiểm tra dependencies
    try:
        import selenium
        print("✅ Selenium available")
    except ImportError:
        print("❌ Selenium not available - run: pip install selenium")
        return
    
    try:
        import undetected_chromedriver
        print("✅ Undetected ChromeDriver available")
    except ImportError:
        print("❌ Undetected ChromeDriver not available - run: pip install undetected-chromedriver")
        return
    
    print()
    
    # Hỏi user có muốn test URL mặc định không
    print(f"🔗 URL mặc định: {test_url}")
    choice = input("Bạn có muốn test URL này? (y/n): ").lower().strip()
    
    if choice == 'n':
        custom_url = input("Nhập URL của bạn: ").strip()
        if custom_url:
            test_url = custom_url
        else:
            print("❌ URL không hợp lệ!")
            return
    
    # Bắt đầu test
    test_url_connection(test_url)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Test bị hủy bởi user")
    except Exception as e:
        print(f"\n💥 Lỗi nghiêm trọng: {e}")
        import traceback
        traceback.print_exc()
