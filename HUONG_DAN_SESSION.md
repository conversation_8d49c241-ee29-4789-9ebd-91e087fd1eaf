# 🌐 Hướng dẫn sử dụng Session Web - PGGame Scatter Predictor

## 📋 Tổng quan

Tính năng Session Web cho phép ứng dụng kết nối trực tiếp với game đang chơi trên trình duyệt và tự động đọc kết quả, gi<PERSON><PERSON> bạn không cần ghi nhận thủ công.

## 🚀 Cách sử dụng

### Bước 1: Mở tab "🌐 Session Web"

1. Khởi động ứng dụng PGGame Scatter Predictor
2. Click vào tab "🌐 Session Web"

### Bước 2: Thêm Session mới

#### 2.1 Điền thông tin session:
- **Tên Session**: Đặt tên dễ nhớ (ví dụ: "Mạt Chược 2 - Table 1")
- **URL Game**: Copy URL từ trình duyệt đang chơi game
- **Loại Game**: Chọn từ dropdown (<PERSON><PERSON><PERSON><PERSON>, Pragmatic Play, Evolution, Khác)
- **Trình duyệt**: <PERSON><PERSON><PERSON> trình duyệt đang sử dụng

#### 2.2 V<PERSON> dụ thông tin:
```
Tên Session: Mạt Chược 2 - VIP Room
URL Game: https://game.example.com/mahjong-ways-2?table=vip1
Loại Game: PGGame
Trình duyệt: Chrome
```

#### 2.3 Click "➕ Thêm Session"

### Bước 3: Kết nối với game

1. **Chọn session** từ danh sách (click vào session muốn kết nối)
2. **Click "🔗 Kết nối"**
3. **Đợi kết nối** - ứng dụng sẽ:
   - Mở trình duyệt mới
   - Điều hướng đến URL game
   - Bắt đầu theo dõi tự động

### Bước 4: Theo dõi kết quả

- **Trạng thái kết nối** hiển thị bằng màu sắc:
  - 🟢 **Xanh**: Đã kết nối và đang hoạt động
  - 🟠 **Cam**: Đang kết nối
  - 🔵 **Xanh dương**: Chưa kết nối
  - ⚫ **Xám**: Tạm dừng
  - 🔴 **Đỏ**: Lỗi

- **Kết quả tự động** hiển thị trong khung "Trạng thái":
  ```
  🔗 Đang kết nối đến Mạt Chược 2 - Table 1...
  ✅ Đã kết nối thành công đến Mạt Chược 2 - Table 1
  🎮 Đang theo dõi game: PGGame
  🎰 10:30:15 - Spin thường
  🎰 10:30:45 - Spin thường
  ⭐ 10:31:20 - SCATTER!
  ```

## 🎮 Các loại game được hỗ trợ

### 1. **PGGame**
- Mạt Chược Ways, Mạt Chược Ways 2
- Fortune Tiger, Fortune Ox
- Candy Burst, Wild Bounty Showdown
- Và tất cả game PG khác

### 2. **Pragmatic Play**
- Sweet Bonanza, Gates of Olympus
- Starlight Princess, Sugar Rush
- Wild West Gold, The Dog House

### 3. **Evolution Gaming**
- Crazy Time, Monopoly Live
- Dream Catcher, Mega Ball

### 4. **Khác**
- Các game slot khác sẽ sử dụng pattern detection chung

## 🔧 Quản lý Session

### Các thao tác có thể thực hiện:

#### 🔗 **Kết nối**
- Kết nối đến session đã chọn
- Bắt đầu theo dõi tự động

#### ⏸️ **Tạm dừng**
- Dừng theo dõi session hiện tại
- Giữ nguyên dữ liệu đã thu thập

#### 🗑️ **Xóa**
- Xóa session khỏi danh sách
- Tự động dừng nếu đang kết nối

#### 🔄 **Làm mới**
- Cập nhật danh sách session
- Làm mới trạng thái kết nối

## 📊 Thông tin Session

Mỗi session hiển thị:
- **Tên và URL** game
- **Số lượng spin** đã theo dõi
- **Số scatter** đã phát hiện
- **Thời gian tạo** và **lần kết nối cuối**
- **Trạng thái** hiện tại

## 🛠️ Xử lý sự cố

### ❌ **Không kết nối được**

**Nguyên nhân có thể:**
- URL không chính xác
- Game chưa load xong
- Trình duyệt bị chặn popup
- Selenium chưa được cài đặt

**Cách khắc phục:**
1. Kiểm tra URL có đúng không
2. Đợi game load hoàn toàn
3. Cho phép popup trong trình duyệt
4. Cài đặt Selenium: `pip install selenium`

### ⚠️ **Phát hiện sai kết quả**

**Nguyên nhân có thể:**
- Game type chọn sai
- Website có cấu trúc khác
- Kết nối mạng không ổn định

**Cách khắc phục:**
1. Chọn đúng loại game
2. Thử kết nối lại
3. Kiểm tra kết nối internet

### 🔄 **Session bị ngắt**

**Nguyên nhân có thể:**
- Trình duyệt bị đóng
- Mất kết nối mạng
- Game website bảo trì

**Cách khắc phục:**
1. Click "🔗 Kết nối" để kết nối lại
2. Kiểm tra trình duyệt còn mở không
3. Thử URL trên trình duyệt thủ công

## 💡 Tips sử dụng hiệu quả

### 1. **Chuẩn bị trước khi kết nối:**
- Đảm bảo game đã load hoàn toàn
- Đăng nhập tài khoản (nếu cần)
- Đặt cược và sẵn sàng chơi

### 2. **Theo dõi nhiều session:**
- Có thể thêm nhiều session khác nhau
- Chỉ kết nối 1 session tại 1 thời điểm
- Chuyển đổi giữa các session dễ dàng

### 3. **Backup dữ liệu:**
- Session được lưu tự động trong `sessions.json`
- Có thể copy file này để backup
- Import lại khi cần thiết

### 4. **Tối ưu hiệu suất:**
- Đóng các tab trình duyệt không cần thiết
- Không chạy quá nhiều ứng dụng nặng
- Đảm bảo RAM đủ cho việc theo dõi

## 🔒 Bảo mật và riêng tư

### ✅ **An toàn:**
- Không lưu trữ thông tin đăng nhập
- Chỉ đọc kết quả game, không can thiệp
- Dữ liệu lưu cục bộ trên máy tính

### ⚠️ **Lưu ý:**
- Không chia sẻ URL session với người khác
- Tắt ứng dụng khi không sử dụng
- Kiểm tra URL trước khi kết nối

## 📞 Hỗ trợ

### Nếu gặp vấn đề:
1. **Kiểm tra log** trong khung "Trạng thái"
2. **Thử kết nối lại** session
3. **Restart ứng dụng** nếu cần
4. **Kiểm tra requirements** đã cài đủ chưa

### Yêu cầu hệ thống:
- **Python 3.7+** với các package cần thiết
- **Chrome/Firefox** browser
- **Selenium WebDriver** 
- **Kết nối internet** ổn định

---

**🎯 Chúc bạn sử dụng hiệu quả tính năng Session Web!**

*Lưu ý: Tính năng này chỉ mang tính hỗ trợ, vẫn nên kết hợp với quan sát thủ công để đảm bảo độ chính xác.*
