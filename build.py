#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for PGGame Scatter Predictor
Script tạo file executable cho tool dự đoán scatter
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'customtkinter',
        'pyinstaller',
        'sqlite3',
        'numpy',
        'matplotlib',
        'seaborn',
        'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('assets', 'assets'),
        ('config.json', '.'),
    ],
    hiddenimports=[
        'customtkinter',
        'tkinter',
        'sqlite3',
        'numpy',
        'matplotlib',
        'seaborn',
        'pandas',
        'PIL',
        'cv2',
        'selenium',
        'requests',
        'bs4'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PGGame_Scatter_Predictor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Created build.spec file")

def prepare_assets():
    """Prepare assets directory"""
    assets_dir = Path('assets')
    assets_dir.mkdir(exist_ok=True)
    
    # Create templates directory
    templates_dir = assets_dir / 'templates'
    templates_dir.mkdir(exist_ok=True)
    
    # Create a simple icon if it doesn't exist
    icon_path = assets_dir / 'icon.ico'
    if not icon_path.exists():
        print("ℹ️ No icon found, you can add icon.ico to assets/ directory")
    
    print("✅ Assets directory prepared")

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building executable...")
    
    try:
        # Run PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'build.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build successful!")
            
            # Check if executable was created
            exe_path = Path('dist/PGGame_Scatter_Predictor.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📦 Executable created: {exe_path}")
                print(f"📏 Size: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Executable not found in dist/ directory")
                return False
        else:
            print("❌ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_installer():
    """Create a simple installer script"""
    installer_content = '''@echo off
echo Installing PGGame Scatter Predictor...

REM Create installation directory
if not exist "C:\\PGGame_Scatter_Predictor" mkdir "C:\\PGGame_Scatter_Predictor"

REM Copy files
copy "PGGame_Scatter_Predictor.exe" "C:\\PGGame_Scatter_Predictor\\"
copy "config.json" "C:\\PGGame_Scatter_Predictor\\" 2>nul

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\PGGame Scatter Predictor.lnk'); $Shortcut.TargetPath = 'C:\\PGGame_Scatter_Predictor\\PGGame_Scatter_Predictor.exe'; $Shortcut.Save()"

echo Installation complete!
echo You can find the shortcut on your desktop.
pause
'''
    
    with open('dist/install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ Created installer script")

def create_readme():
    """Create README for distribution"""
    readme_content = '''# PGGame Scatter Predictor

## Hướng dẫn cài đặt

### Cách 1: Chạy trực tiếp
1. Giải nén file zip
2. Chạy file `PGGame_Scatter_Predictor.exe`

### Cách 2: Cài đặt vào hệ thống
1. Giải nén file zip
2. Chạy file `install.bat` với quyền Administrator
3. Sử dụng shortcut trên Desktop

## Hướng dẫn sử dụng

### Bước 1: Cài đặt game
1. Mở tab "⚙️ Cài đặt"
2. Nhập tỷ lệ scatter của game (ví dụ: 3.0 cho 3%)
3. Nhập số scatter tối thiểu cần thiết (thường là 3)
4. Nhấn "💾 Lưu cài đặt"

### Bước 2: Ghi nhận kết quả
1. Mở tab "🎯 Dự đoán"
2. Mỗi lần spin:
   - Nhấn "🎰 Spin thường" nếu không ra scatter
   - Nhấn "⭐ Ra Scatter" nếu ra scatter
3. Tool sẽ tự động cập nhật dự đoán

### Bước 3: Xem thống kê
1. Tab "📈 Thống kê": Xem phân tích chi tiết
2. Tab "📝 Lịch sử": Xem lịch sử các lần spin
3. Tab "🤖 Tự động": Chế độ tự động (thử nghiệm)

## Tính năng chính

- ✅ Dự đoán xác suất ra scatter
- ✅ Thống kê chi tiết và phân tích streak
- ✅ Lưu trữ lịch sử spin
- ✅ Xuất/nhập dữ liệu
- ✅ Giao diện thân thiện
- ⚠️ Chế độ tự động (thử nghiệm)

## Lưu ý quan trọng

⚠️ **Tool chỉ mang tính tham khảo**
- Kết quả game slot là hoàn toàn ngẫu nhiên
- Không có thuật toán nào có thể dự đoán chính xác 100%
- Luôn chơi có trách nhiệm và đặt giới hạn

## Yêu cầu hệ thống

- Windows 10/11
- RAM: 4GB trở lên
- Dung lượng: 100MB trống

## Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra Windows Defender/Antivirus
2. Chạy với quyền Administrator
3. Kiểm tra file log (nếu có)

---
**Chúc bạn may mắn! 🍀**
'''
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ Created README.txt")

def cleanup():
    """Clean up build files"""
    cleanup_dirs = ['build', '__pycache__']
    cleanup_files = ['build.spec']
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 Cleaned up {dir_name}/")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"🧹 Cleaned up {file_name}")

def main():
    """Main build process"""
    print("🎰 PGGame Scatter Predictor - Build Script")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return False
    
    # Prepare for build
    prepare_assets()
    create_spec_file()
    
    # Build executable
    if not build_executable():
        return False
    
    # Create additional files
    create_installer()
    create_readme()
    
    # Cleanup
    cleanup()
    
    print("\n🎉 Build process completed successfully!")
    print("📁 Check the 'dist/' directory for your executable")
    print("📋 Files created:")
    print("   - PGGame_Scatter_Predictor.exe")
    print("   - install.bat")
    print("   - README.txt")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
