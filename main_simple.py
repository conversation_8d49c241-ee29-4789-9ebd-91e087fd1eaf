#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PGGame Scatter Predictor - Simple Version
Phiên bản đơn giản không có Selenium
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import sqlite3
import json
import os
import webbrowser
from datetime import datetime
from typing import Dict, List

# Import custom modules (without scraper)
from database import DatabaseManager
from predictor import ScatterPredictor
from config import ConfigManager

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class PGGameScatterToolSimple:
    """Simple version without Selenium"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🎰 PGGame Scatter Predictor (Simple)")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Initialize managers
        self.db_manager = DatabaseManager()
        self.config_manager = ConfigManager()
        self.predictor = ScatterPredictor(self.db_manager, self.config_manager)
        
        # Variables
        self.current_streak = tk.IntVar(value=0)
        self.total_spins = tk.IntVar(value=0)
        self.total_scatters = tk.IntVar(value=0)
        self.scatter_rate = tk.DoubleVar(value=3.0)
        self.min_scatters = tk.IntVar(value=3)
        
        # Session management
        self.sessions = []
        self.active_session = None
        
        # Setup UI
        self.setup_ui()
        self.load_data()
        self.load_sessions()
        self.update_stats()

        # Calculate initial prediction
        self.calculate_prediction()
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Create main container
        self.main_container = ctk.CTkFrame(self.root)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        self.notebook = ctk.CTkTabview(self.main_container)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create tabs
        self.setup_main_tab()
        self.setup_settings_tab()
        self.setup_session_tab()
        self.setup_stats_tab()
        
    def setup_main_tab(self):
        """Setup main prediction tab"""
        main_tab = self.notebook.add("🎯 Dự đoán")
        
        # Current stats frame
        stats_frame = ctk.CTkFrame(main_tab)
        stats_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(stats_frame, text="📊 Thống kê hiện tại", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Stats grid
        stats_grid = ctk.CTkFrame(stats_frame)
        stats_grid.pack(fill="x", padx=10, pady=5)
        
        # Row 1
        ctk.CTkLabel(stats_grid, text="Spin liên tiếp:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        self.streak_label = ctk.CTkLabel(stats_grid, text="0", font=ctk.CTkFont(size=16, weight="bold"))
        self.streak_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        ctk.CTkLabel(stats_grid, text="Tổng spin:").grid(row=0, column=2, padx=10, pady=5, sticky="w")
        self.total_spins_label = ctk.CTkLabel(stats_grid, text="0", font=ctk.CTkFont(size=16, weight="bold"))
        self.total_spins_label.grid(row=0, column=3, padx=10, pady=5, sticky="w")
        
        # Row 2
        ctk.CTkLabel(stats_grid, text="Scatter đã ra:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.scatters_label = ctk.CTkLabel(stats_grid, text="0", font=ctk.CTkFont(size=16, weight="bold"))
        self.scatters_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        ctk.CTkLabel(stats_grid, text="Tỷ lệ thực tế:").grid(row=1, column=2, padx=10, pady=5, sticky="w")
        self.actual_rate_label = ctk.CTkLabel(stats_grid, text="0%", font=ctk.CTkFont(size=16, weight="bold"))
        self.actual_rate_label.grid(row=1, column=3, padx=10, pady=5, sticky="w")
        
        # Active session display
        session_frame = ctk.CTkFrame(stats_frame)
        session_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(session_frame, text="🎮 Session hiện tại:").pack(side="left", padx=10)
        self.active_session_label = ctk.CTkLabel(session_frame, text="Chưa chọn session", 
                                               font=ctk.CTkFont(size=14, weight="bold"))
        self.active_session_label.pack(side="left", padx=10)
        
        # Prediction frame
        prediction_frame = ctk.CTkFrame(main_tab)
        prediction_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(prediction_frame, text="🔮 Dự đoán", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Prediction display
        self.prediction_text = ctk.CTkTextbox(prediction_frame, height=150)
        self.prediction_text.pack(fill="x", padx=10, pady=5)
        
        # Action buttons frame
        actions_frame = ctk.CTkFrame(main_tab)
        actions_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(actions_frame, text="🎰 Ghi nhận kết quả", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        buttons_frame = ctk.CTkFrame(actions_frame)
        buttons_frame.pack(pady=10)
        
        # Action buttons
        ctk.CTkButton(buttons_frame, text="🎰 Spin thường", 
                     command=self.record_normal_spin,
                     width=150, height=40).pack(side="left", padx=10)
        
        ctk.CTkButton(buttons_frame, text="⭐ Ra Scatter", 
                     command=self.record_scatter,
                     width=150, height=40).pack(side="left", padx=10)
        
        ctk.CTkButton(buttons_frame, text="🎯 Tính toán dự đoán", 
                     command=self.calculate_prediction,
                     width=150, height=40).pack(side="left", padx=10)
        
        ctk.CTkButton(buttons_frame, text="🔄 Reset dữ liệu",
                     command=self.reset_data,
                     width=150, height=40).pack(side="left", padx=10)

        # Test buttons frame (for debugging)
        test_frame = ctk.CTkFrame(actions_frame)
        test_frame.pack(pady=5)

        ctk.CTkLabel(test_frame, text="🧪 Test (Debug)",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        test_buttons_frame = ctk.CTkFrame(test_frame)
        test_buttons_frame.pack(pady=5)

        ctk.CTkButton(test_buttons_frame, text="🔮 Test Dự đoán",
                     command=self.test_prediction,
                     width=120, height=30).pack(side="left", padx=5)

        ctk.CTkButton(test_buttons_frame, text="📊 Test Stats",
                     command=self.test_stats,
                     width=120, height=30).pack(side="left", padx=5)

    def setup_settings_tab(self):
        """Setup settings tab"""
        settings_tab = self.notebook.add("⚙️ Cài đặt")
        
        # Game settings frame
        game_frame = ctk.CTkFrame(settings_tab)
        game_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(game_frame, text="🎮 Cài đặt Game", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # Scatter rate setting
        rate_frame = ctk.CTkFrame(game_frame)
        rate_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(rate_frame, text="Tỷ lệ Scatter (%):").pack(side="left", padx=10)
        self.rate_entry = ctk.CTkEntry(rate_frame, textvariable=self.scatter_rate, width=100)
        self.rate_entry.pack(side="left", padx=10)
        
        # Min scatters setting
        min_frame = ctk.CTkFrame(game_frame)
        min_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(min_frame, text="Số scatter tối thiểu:").pack(side="left", padx=10)
        self.min_entry = ctk.CTkEntry(min_frame, textvariable=self.min_scatters, width=100)
        self.min_entry.pack(side="left", padx=10)
        
        # Save button
        ctk.CTkButton(game_frame, text="💾 Lưu cài đặt", 
                     command=self.save_settings).pack(pady=10)

    def setup_session_tab(self):
        """Setup session management tab"""
        session_tab = self.notebook.add("🌐 Session")
        
        # Info frame
        info_frame = ctk.CTkFrame(session_tab)
        info_frame.pack(fill="x", padx=10, pady=5)
        
        info_text = """
🌐 QUẢN LÝ SESSION GAME

💡 Chế độ thủ công: Do vấn đề tương thích Selenium, 
   tool sẽ hoạt động ở chế độ thủ công.

✅ Cách sử dụng:
1. Thêm session với URL game
2. Click "Kết nối" để mở game trong trình duyệt
3. Ghi nhận kết quả thủ công ở tab "🎯 Dự đoán"
"""
        
        ctk.CTkLabel(info_frame, text=info_text, justify="left").pack(padx=10, pady=10)
        
        # Add session frame
        add_frame = ctk.CTkFrame(session_tab)
        add_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(add_frame, text="➕ Thêm Session", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)
        
        # Session name
        name_frame = ctk.CTkFrame(add_frame)
        name_frame.pack(fill="x", padx=10, pady=2)
        ctk.CTkLabel(name_frame, text="Tên:").pack(side="left", padx=5)
        self.session_name_entry = ctk.CTkEntry(name_frame, placeholder_text="Mạt Chược 2 - Table 1")
        self.session_name_entry.pack(side="right", fill="x", expand=True, padx=5)
        
        # Game URL
        url_frame = ctk.CTkFrame(add_frame)
        url_frame.pack(fill="x", padx=10, pady=2)
        ctk.CTkLabel(url_frame, text="URL:").pack(side="left", padx=5)
        self.session_url_entry = ctk.CTkEntry(url_frame, placeholder_text="https://...")
        self.session_url_entry.pack(side="right", fill="x", expand=True, padx=5)
        
        # Add button
        ctk.CTkButton(add_frame, text="➕ Thêm Session", 
                     command=self.add_session).pack(pady=10)
        
        # Session list
        list_frame = ctk.CTkFrame(session_tab)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        ctk.CTkLabel(list_frame, text="📋 Danh sách Session", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)
        
        self.session_listbox = ctk.CTkScrollableFrame(list_frame)
        self.session_listbox.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Control buttons
        control_frame = ctk.CTkFrame(list_frame)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkButton(control_frame, text="🔗 Kết nối", 
                     command=self.connect_session, width=100).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="🗑️ Xóa", 
                     command=self.delete_session, width=100).pack(side="left", padx=5)
        ctk.CTkButton(control_frame, text="🔄 Làm mới", 
                     command=self.refresh_sessions, width=100).pack(side="left", padx=5)

    def setup_stats_tab(self):
        """Setup statistics tab"""
        stats_tab = self.notebook.add("📈 Thống kê")
        
        # Stats display
        self.stats_text = ctk.CTkTextbox(stats_tab, height=400)
        self.stats_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Refresh button
        ctk.CTkButton(stats_tab, text="🔄 Cập nhật thống kê", 
                     command=self.update_detailed_stats).pack(pady=10)

    # Session management methods
    def add_session(self):
        """Add new session"""
        name = self.session_name_entry.get().strip()
        url = self.session_url_entry.get().strip()
        
        if not name or not url:
            messagebox.showerror("Lỗi", "Vui lòng nhập đầy đủ tên và URL!")
            return
        
        session = {
            'id': len(self.sessions) + 1,
            'name': name,
            'url': url,
            'status': 'Chưa kết nối',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_spins': 0,
            'total_scatters': 0
        }
        
        self.sessions.append(session)
        self.save_sessions()
        self.refresh_sessions()
        
        # Clear form
        self.session_name_entry.delete(0, 'end')
        self.session_url_entry.delete(0, 'end')
        
        messagebox.showinfo("Thành công", f"Đã thêm session '{name}'!")

    def connect_session(self):
        """Connect to selected session (manual mode)"""
        if not self.sessions:
            messagebox.showwarning("Cảnh báo", "Chưa có session nào!")
            return
        
        # Use first session for demo
        session = self.sessions[0]
        
        try:
            # Set as active session
            self.active_session = session
            session['status'] = 'Chế độ thủ công'
            
            # Update UI
            self.active_session_label.configure(text=session['name'])
            
            # Open URL in browser
            webbrowser.open(session['url'])
            
            # Show guide
            guide = f"""
🎮 ĐÃ KẾT NỐI SESSION: {session['name']}

🌐 URL đã được mở trong trình duyệt mặc định

📋 HƯỚNG DẪN:
1. Chuyển sang trình duyệt và chơi game
2. Quay lại ứng dụng này
3. Mỗi lần spin:
   - Không ra scatter → Click "🎰 Spin thường"
   - Ra scatter → Click "⭐ Ra Scatter"

✅ Tool sẽ tự động cập nhật dự đoán!
"""
            
            messagebox.showinfo("Kết nối thành công", guide)
            self.refresh_sessions()
            
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể mở URL: {e}")

    def delete_session(self):
        """Delete selected session"""
        if not self.sessions:
            messagebox.showwarning("Cảnh báo", "Chưa có session nào!")
            return
        
        # Use first session for demo
        session = self.sessions[0]
        
        if messagebox.askyesno("Xác nhận", f"Xóa session '{session['name']}'?"):
            self.sessions.remove(session)
            if self.active_session == session:
                self.active_session = None
                self.active_session_label.configure(text="Chưa chọn session")
            
            self.save_sessions()
            self.refresh_sessions()
            messagebox.showinfo("Thành công", "Đã xóa session!")

    def refresh_sessions(self):
        """Refresh session list display"""
        try:
            # Clear current list
            for widget in self.session_listbox.winfo_children():
                widget.destroy()
            
            if not self.sessions:
                ctk.CTkLabel(self.session_listbox, text="Chưa có session nào").pack(pady=10)
                return
            
            for session in self.sessions:
                session_frame = ctk.CTkFrame(self.session_listbox)
                session_frame.pack(fill="x", padx=5, pady=2)
                
                # Session info
                info_text = f"🎮 {session['name']}\n🌐 {session['url'][:50]}...\n🔗 {session['status']}"
                
                session_label = ctk.CTkLabel(session_frame, text=info_text, justify="left")
                session_label.pack(side="left", fill="x", expand=True, padx=10, pady=5)
                
                # Status indicator
                status_color = {
                    'Chế độ thủ công': 'green',
                    'Chưa kết nối': 'blue'
                }.get(session['status'], 'gray')
                
                status_indicator = ctk.CTkLabel(session_frame, text="●", 
                                              text_color=status_color, font=ctk.CTkFont(size=20))
                status_indicator.pack(side="right", padx=10)
                
        except Exception as e:
            print(f"Error refreshing sessions: {e}")

    # Core functionality methods
    def record_normal_spin(self):
        """Record a normal spin (no scatter)"""
        try:
            print("🎰 Ghi nhận: Spin thường")

            # Add to database
            self.db_manager.add_spin_result(False)

            # Update variables
            self.current_streak.set(self.current_streak.get() + 1)
            self.total_spins.set(self.total_spins.get() + 1)

            # Update active session
            if self.active_session:
                self.active_session['total_spins'] += 1
                self.save_sessions()

            # Update UI and prediction
            self.update_stats()
            self.calculate_prediction()

            print(f"✅ Đã cập nhật: Streak = {self.current_streak.get()}, Total = {self.total_spins.get()}")

        except Exception as e:
            print(f"❌ Lỗi ghi nhận spin thường: {e}")
            import traceback
            traceback.print_exc()
        
    def record_scatter(self):
        """Record a scatter result"""
        try:
            print("⭐ Ghi nhận: Ra Scatter!")

            # Add to database
            self.db_manager.add_spin_result(True)

            # Update variables
            self.current_streak.set(0)  # Reset streak
            self.total_spins.set(self.total_spins.get() + 1)
            self.total_scatters.set(self.total_scatters.get() + 1)

            # Update active session
            if self.active_session:
                self.active_session['total_spins'] += 1
                self.active_session['total_scatters'] += 1
                self.save_sessions()

            # Update UI and prediction
            self.update_stats()
            self.calculate_prediction()

            print(f"✅ Đã cập nhật: Streak = 0 (reset), Total = {self.total_spins.get()}, Scatters = {self.total_scatters.get()}")

        except Exception as e:
            print(f"❌ Lỗi ghi nhận scatter: {e}")
            import traceback
            traceback.print_exc()

    def calculate_prediction(self):
        """Calculate and display prediction"""
        try:
            print("🔮 Đang tính toán dự đoán...")

            # Get prediction from predictor
            prediction = self.predictor.get_prediction()

            # Update prediction display
            self.prediction_text.delete("1.0", "end")
            self.prediction_text.insert("1.0", prediction)

            # Force UI update
            self.prediction_text.update()

            print("✅ Dự đoán đã được cập nhật")

        except Exception as e:
            print(f"❌ Lỗi tính toán dự đoán: {e}")
            import traceback
            traceback.print_exc()

            # Show error in prediction text
            error_msg = f"❌ Lỗi tính toán dự đoán: {e}\n\nVui lòng thử lại hoặc kiểm tra cài đặt."
            self.prediction_text.delete("1.0", "end")
            self.prediction_text.insert("1.0", error_msg)

    def update_stats(self):
        """Update statistics display"""
        # Update labels
        self.streak_label.configure(text=str(self.current_streak.get()))
        self.total_spins_label.configure(text=str(self.total_spins.get()))
        self.scatters_label.configure(text=str(self.total_scatters.get()))
        
        # Calculate actual rate
        if self.total_spins.get() > 0:
            actual_rate = (self.total_scatters.get() / self.total_spins.get()) * 100
            self.actual_rate_label.configure(text=f"{actual_rate:.2f}%")
        else:
            self.actual_rate_label.configure(text="0%")

    def update_detailed_stats(self):
        """Update detailed statistics display"""
        stats = self.db_manager.get_current_stats()
        
        stats_text = f"""
📊 THỐNG KÊ CHI TIẾT

🎯 Tổng quan:
• Tổng số spin: {stats['total_spins']}
• Tổng scatter: {stats['total_scatters']}
• Tỷ lệ thực tế: {stats['actual_rate']:.2f}%
• Streak hiện tại: {stats['current_streak']}
• Streak dài nhất: {stats['longest_streak']}
• Trung bình spin/scatter: {stats['avg_spins_per_scatter']:.1f}

🎮 Session hiện tại: {self.active_session['name'] if self.active_session else 'Chưa chọn'}

📈 Phân tích:
• Tình hình: {'Bình thường' if stats['current_streak'] < 20 else 'Cần chú ý' if stats['current_streak'] < 50 else 'Rủi ro cao'}
• Khuyến nghị: {'Tiếp tục chơi' if stats['current_streak'] < 30 else 'Cân nhắc nghỉ ngơi'}

⏰ Cập nhật: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}
"""
        
        self.stats_text.delete("1.0", "end")
        self.stats_text.insert("1.0", stats_text)

    def load_data(self):
        """Load data from database"""
        stats = self.db_manager.get_current_stats()
        self.current_streak.set(stats.get('current_streak', 0))
        self.total_spins.set(stats.get('total_spins', 0))
        self.total_scatters.set(stats.get('total_scatters', 0))
        
        # Load settings
        settings = self.config_manager.get_settings()
        self.scatter_rate.set(settings.get('scatter_rate', 3.0))
        self.min_scatters.set(settings.get('min_scatters', 3))

    def save_settings(self):
        """Save current settings"""
        settings = {
            'scatter_rate': self.scatter_rate.get(),
            'min_scatters': self.min_scatters.get()
        }
        self.config_manager.save_settings(settings)
        messagebox.showinfo("Thành công", "Đã lưu cài đặt!")

    def reset_data(self):
        """Reset all data"""
        if messagebox.askyesno("Xác nhận", "Bạn có chắc muốn xóa tất cả dữ liệu?"):
            self.db_manager.reset_data()
            self.current_streak.set(0)
            self.total_spins.set(0)
            self.total_scatters.set(0)
            self.update_stats()
            self.calculate_prediction()  # Recalculate after reset
            messagebox.showinfo("Thành công", "Đã reset dữ liệu!")

    def test_prediction(self):
        """Test prediction function"""
        try:
            print("🧪 Testing prediction...")

            # Get current stats
            stats = self.db_manager.get_current_stats()
            settings = self.config_manager.get_settings()

            print(f"Stats: {stats}")
            print(f"Settings: {settings}")

            # Force calculate prediction
            self.calculate_prediction()

            messagebox.showinfo("Test", f"Prediction test completed!\nCheck console for details.")

        except Exception as e:
            print(f"❌ Test prediction error: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("Test Error", f"Prediction test failed: {e}")

    def test_stats(self):
        """Test stats function"""
        try:
            print("🧪 Testing stats...")

            # Get and display stats
            stats = self.db_manager.get_current_stats()

            stats_info = f"""
📊 Current Stats:
• Total spins: {stats.get('total_spins', 0)}
• Total scatters: {stats.get('total_scatters', 0)}
• Current streak: {stats.get('current_streak', 0)}
• Actual rate: {stats.get('actual_rate', 0):.2f}%

🎮 UI Variables:
• Streak: {self.current_streak.get()}
• Total spins: {self.total_spins.get()}
• Total scatters: {self.total_scatters.get()}
"""

            print(stats_info)
            messagebox.showinfo("Stats Test", stats_info)

        except Exception as e:
            print(f"❌ Test stats error: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("Test Error", f"Stats test failed: {e}")

    def save_sessions(self):
        """Save sessions to file"""
        try:
            with open('sessions.json', 'w', encoding='utf-8') as f:
                json.dump(self.sessions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving sessions: {e}")

    def load_sessions(self):
        """Load sessions from file"""
        try:
            if os.path.exists('sessions.json'):
                with open('sessions.json', 'r', encoding='utf-8') as f:
                    self.sessions = json.load(f)
        except Exception as e:
            print(f"Error loading sessions: {e}")
            self.sessions = []

    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """Handle application closing"""
        self.root.destroy()

if __name__ == "__main__":
    app = PGGameScatterToolSimple()
    app.run()
