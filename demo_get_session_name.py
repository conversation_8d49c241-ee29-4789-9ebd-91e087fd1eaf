#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo: <PERSON><PERSON><PERSON> c<PERSON>ch lấy tên session trong PGGame Scatter Predictor
"""

# G<PERSON><PERSON> lập class chính của ứng dụng
class SessionManager:
    def __init__(self):
        # D<PERSON> liệu session mẫu
        self.sessions = [
            {
                'id': 1,
                'name': 'Mạt Chược 2 - Table 1',
                'url': 'https://game.example.com/mahjong-ways-2',
                'game_type': 'PGGame',
                'status': 'Đã kết nối',
                'total_spins': 150,
                'total_scatters': 8,
                'created_at': '2024-06-03 10:00:00'
            },
            {
                'id': 2,
                'name': 'Fortune Tiger - VIP',
                'url': 'https://game.example.com/fortune-tiger',
                'game_type': 'PGGame',
                'status': 'Tạm dừng',
                'total_spins': 89,
                'total_scatters': 3,
                'created_at': '2024-06-03 11:30:00'
            },
            {
                'id': 3,
                'name': 'Sweet Bonanza - Demo',
                'url': 'https://demo.pragmaticplay.net/sweet-bonanza',
                'game_type': 'Pragmatic Play',
                'status': 'Chưa kết nối',
                'total_spins': 0,
                'total_scatters': 0,
                'created_at': '2024-06-03 12:00:00'
            }
        ]
        
        # Session đang hoạt động
        self.active_session = self.sessions[0]  # Mạt Chược 2 đang active

    def get_active_session_name(self):
        """Lấy tên session đang hoạt động"""
        if self.active_session:
            return self.active_session['name']
        return None

    def get_session_name_by_id(self, session_id):
        """Lấy tên session theo ID"""
        for session in self.sessions:
            if session['id'] == session_id:
                return session['name']
        return None

    def get_all_session_names(self):
        """Lấy danh sách tất cả tên session"""
        return [session['name'] for session in self.sessions]

    def find_session_by_name(self, name):
        """Tìm session theo tên"""
        for session in self.sessions:
            if session['name'] == name:
                return session
        return None

    def get_session_info(self, session_name=None):
        """Lấy thông tin chi tiết session"""
        if session_name:
            session = self.find_session_by_name(session_name)
        else:
            session = self.active_session
        
        if session:
            return {
                'name': session['name'],
                'url': session['url'],
                'game_type': session['game_type'],
                'status': session['status'],
                'total_spins': session['total_spins'],
                'total_scatters': session['total_scatters'],
                'created_at': session['created_at']
            }
        return None

def demo_get_session_names():
    """Demo các cách lấy tên session"""
    
    print("🎰 Demo: Các cách lấy tên session")
    print("=" * 50)
    
    # Khởi tạo session manager
    manager = SessionManager()
    
    print("\n1️⃣ Lấy tên session đang hoạt động:")
    active_name = manager.get_active_session_name()
    print(f"   Session đang hoạt động: {active_name}")
    
    print("\n2️⃣ Lấy tên session theo ID:")
    for session_id in [1, 2, 3, 999]:
        name = manager.get_session_name_by_id(session_id)
        print(f"   ID {session_id}: {name if name else 'Không tìm thấy'}")
    
    print("\n3️⃣ Lấy danh sách tất cả tên session:")
    all_names = manager.get_all_session_names()
    for i, name in enumerate(all_names, 1):
        print(f"   {i}. {name}")
    
    print("\n4️⃣ Tìm session theo tên:")
    search_names = [
        'Mạt Chược 2 - Table 1',
        'Fortune Tiger - VIP',
        'Session không tồn tại'
    ]
    
    for name in search_names:
        session = manager.find_session_by_name(name)
        if session:
            print(f"   ✅ Tìm thấy: {name} (ID: {session['id']})")
        else:
            print(f"   ❌ Không tìm thấy: {name}")
    
    print("\n5️⃣ Lấy thông tin chi tiết session:")
    
    # Thông tin session đang hoạt động
    print("   📊 Session đang hoạt động:")
    active_info = manager.get_session_info()
    if active_info:
        print(f"      Tên: {active_info['name']}")
        print(f"      Game: {active_info['game_type']}")
        print(f"      Trạng thái: {active_info['status']}")
        print(f"      Spins: {active_info['total_spins']}")
        print(f"      Scatters: {active_info['total_scatters']}")
    
    # Thông tin session theo tên
    print("\n   📊 Session theo tên 'Fortune Tiger - VIP':")
    tiger_info = manager.get_session_info('Fortune Tiger - VIP')
    if tiger_info:
        print(f"      Tên: {tiger_info['name']}")
        print(f"      Game: {tiger_info['game_type']}")
        print(f"      Trạng thái: {tiger_info['status']}")
        print(f"      Spins: {tiger_info['total_spins']}")
        print(f"      Scatters: {tiger_info['total_scatters']}")

def demo_practical_usage():
    """Demo sử dụng thực tế"""
    
    print("\n\n🎯 Demo: Sử dụng thực tế")
    print("=" * 50)
    
    manager = SessionManager()
    
    # Scenario 1: Hiển thị session đang chơi
    print("\n📱 Scenario 1: Hiển thị thông tin session đang chơi")
    active_name = manager.get_active_session_name()
    if active_name:
        print(f"   🎮 Đang chơi: {active_name}")
        
        # Lấy thêm thông tin
        info = manager.get_session_info()
        if info:
            rate = (info['total_scatters'] / info['total_spins'] * 100) if info['total_spins'] > 0 else 0
            print(f"   📊 Đã spin: {info['total_spins']} lần")
            print(f"   ⭐ Scatter: {info['total_scatters']} lần ({rate:.1f}%)")
    else:
        print("   ❌ Không có session nào đang hoạt động")
    
    # Scenario 2: Menu chọn session
    print("\n📋 Scenario 2: Menu chọn session")
    all_names = manager.get_all_session_names()
    print("   Chọn session để kết nối:")
    for i, name in enumerate(all_names, 1):
        session = manager.find_session_by_name(name)
        status_icon = {
            'Đã kết nối': '🟢',
            'Tạm dừng': '⚫',
            'Chưa kết nối': '🔵',
            'Lỗi': '🔴'
        }.get(session['status'], '⚪')
        
        print(f"   {i}. {status_icon} {name}")
    
    # Scenario 3: Thống kê tổng quan
    print("\n📈 Scenario 3: Thống kê tổng quan")
    total_spins = sum(s['total_spins'] for s in manager.sessions)
    total_scatters = sum(s['total_scatters'] for s in manager.sessions)
    active_sessions = len([s for s in manager.sessions if s['status'] == 'Đã kết nối'])
    
    print(f"   📊 Tổng số session: {len(manager.sessions)}")
    print(f"   🟢 Session đang hoạt động: {active_sessions}")
    print(f"   🎰 Tổng spins: {total_spins}")
    print(f"   ⭐ Tổng scatters: {total_scatters}")
    if total_spins > 0:
        overall_rate = total_scatters / total_spins * 100
        print(f"   📈 Tỷ lệ scatter tổng: {overall_rate:.2f}%")

def demo_error_handling():
    """Demo xử lý lỗi"""
    
    print("\n\n⚠️ Demo: Xử lý lỗi")
    print("=" * 50)
    
    manager = SessionManager()
    
    # Test với session không tồn tại
    print("\n🔍 Test với session không tồn tại:")
    
    # Tìm session không tồn tại
    fake_session = manager.find_session_by_name("Session không tồn tại")
    print(f"   Tìm session fake: {fake_session}")
    
    # Lấy tên theo ID không tồn tại
    fake_name = manager.get_session_name_by_id(999)
    print(f"   Tên session ID 999: {fake_name}")
    
    # Lấy info session không tồn tại
    fake_info = manager.get_session_info("Session fake")
    print(f"   Info session fake: {fake_info}")
    
    # Test khi không có active session
    print("\n🚫 Test khi không có active session:")
    manager.active_session = None
    no_active = manager.get_active_session_name()
    print(f"   Tên active session: {no_active}")
    
    no_active_info = manager.get_session_info()
    print(f"   Info active session: {no_active_info}")

if __name__ == "__main__":
    # Chạy tất cả demo
    demo_get_session_names()
    demo_practical_usage()
    demo_error_handling()
    
    print("\n\n✅ Demo hoàn thành!")
    print("💡 Sử dụng các phương thức này trong ứng dụng chính để lấy tên session.")
