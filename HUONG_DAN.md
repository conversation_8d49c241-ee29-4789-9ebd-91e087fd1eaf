# 🎰 Hướng dẫn sử dụng PGGame Scatter Predictor

## 📋 <PERSON><PERSON> lụ<PERSON>
1. [<PERSON><PERSON><PERSON> đặt](#cài-đặt)
2. [Khởi động](#khởi-động)
3. [Cài đặt game](#cài-đặt-game)
4. [Sử dụng cơ bản](#sử-dụng-cơ-bản)
5. [Tính năng nâng cao](#tính-năng-nâng-cao)
6. [Xử lý sự cố](#xử-lý-sự-cố)

## 🔧 Cài đặt

### Yêu cầu hệ thống
- **Hệ điều hành**: Windows 10/11
- **Python**: 3.7 trở lên (nếu chạy từ source code)
- **RAM**: 4GB trở lên
- **Dung lượng**: 200MB trống

### Cách 1: Sử dụng file executable (.exe)
1. Tải file `PGGame_Scatter_Predictor.exe`
2. Ch<PERSON><PERSON> trự<PERSON> tiếp file .exe
3. Hoặc sử dụng file `install.bat` đ<PERSON> cài đặt và<PERSON> hệ thống

### Cách 2: Ch<PERSON>y từ source code
1. Cài đặt Python 3.7+
2. Cài đặt dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Chạy ứng dụng:
   ```bash
   python run.py
   ```

## 🚀 Khởi động

### Lần đầu sử dụng
1. Khởi động ứng dụng
2. Tool sẽ tự động tạo database và file cấu hình
3. Giao diện chính sẽ hiển thị với 5 tab:
   - 🎯 **Dự đoán**: Tab chính để ghi nhận kết quả
   - ⚙️ **Cài đặt**: Cấu hình game và tool
   - 📈 **Thống kê**: Xem phân tích chi tiết
   - 📝 **Lịch sử**: Xem lịch sử spin
   - 🤖 **Tự động**: Chế độ tự động (thử nghiệm)

## ⚙️ Cài đặt game

### Bước 1: Mở tab "Cài đặt"
1. Click vào tab "⚙️ Cài đặt"
2. Nhập thông tin game:

### Bước 2: Cấu hình tỷ lệ scatter
- **Tỷ lệ Scatter (%)**: Nhập tỷ lệ scatter của game
  - Ví dụ: `3.0` cho 3%
  - Ví dụ: `2.5` cho 2.5%
  - Thông tin này thường có trong bảng paytable của game

### Bước 3: Số scatter tối thiểu
- **Số scatter tối thiểu**: Số scatter cần thiết để trigger bonus
  - Thường là `3` cho hầu hết game PG
  - Một số game có thể là `4` hoặc `5`

### Bước 4: Lưu cài đặt
- Click "💾 Lưu cài đặt"
- Tool sẽ hiển thị thông báo xác nhận

## 🎮 Sử dụng cơ bản

### Tab "🎯 Dự đoán"

#### Ghi nhận kết quả spin
1. **Spin thường**: Click "🎰 Spin thường" khi spin không ra scatter
2. **Ra Scatter**: Click "⭐ Ra Scatter" khi spin ra scatter
3. Tool sẽ tự động:
   - Cập nhật thống kê
   - Tính toán dự đoán mới
   - Hiển thị xác suất

#### Đọc thông tin dự đoán
- **Spin liên tiếp**: Số spin liên tiếp chưa ra scatter
- **Tổng spin**: Tổng số lần đã spin
- **Scatter đã ra**: Số lần đã ra scatter
- **Tỷ lệ thực tế**: Tỷ lệ scatter thực tế từ dữ liệu

#### Hiểu dự đoán
```
🔮 DỰ ĐOÁN SCATTER
• Dự đoán cơ bản: 15 spin
• Dự đoán nâng cao: 12 spin
• Độ tin cậy: 75.0%

⚠️ MỨC ĐỘ RỦI RO: CAO

📈 XÁC SUẤT RA SCATTER:
• Trong 5 spin tiếp: 15.2%
• Trong 10 spin tiếp: 28.7%
• Trong 20 spin tiếp: 48.6%
• Trong 50 spin tiếp: 78.1%
```

### Tab "📈 Thống kê"

#### Xem thống kê chi tiết
- Click "🔄 Cập nhật thống kê" để làm mới
- Xem phân tích streak và pattern
- Theo dõi hiệu suất dự đoán

### Tab "📝 Lịch sử"

#### Xem lịch sử spin
- Hiển thị 50 lần spin gần nhất
- Format: `Thời gian: Kết quả`
- Click "🔄 Làm mới" để cập nhật

## 🔧 Tính năng nâng cao

### Xuất/Nhập dữ liệu
1. **Menu Tệp** → **Xuất dữ liệu**: Lưu dữ liệu ra file JSON
2. **Menu Tệp** → **Nhập dữ liệu**: Khôi phục từ file JSON

### Reset dữ liệu
1. Click "🔄 Reset dữ liệu" trong tab Dự đoán
2. Hoặc **Menu Công cụ** → **Reset dữ liệu**
3. Xác nhận để xóa toàn bộ dữ liệu

### Chế độ tự động (Thử nghiệm)
⚠️ **Lưu ý**: Tính năng này đang trong giai đoạn thử nghiệm

1. Mở tab "🤖 Tự động"
2. Bật "Chế độ tự động"
3. Tool sẽ cố gắng tự động đọc kết quả từ game

## 🔍 Hiểu thuật toán dự đoán

### Dự đoán cơ bản
- Dựa trên tỷ lệ scatter đã cài đặt
- Công thức: `Số spin dự đoán = 1 / tỷ lệ scatter`

### Dự đoán nâng cao
- Phân tích streak hiện tại
- Xem xét pattern lịch sử
- Điều chỉnh theo thời gian trong ngày
- Tính toán độ tin cậy

### Mức độ rủi ro
- **THẤP**: Streak ngắn, tình hình bình thường
- **TRUNG BÌNH**: Streak trung bình, cần thận trọng
- **CAO**: Streak dài, rủi ro cao
- **RẤT CAO**: Streak rất dài, nên nghỉ ngơi

## ⚠️ Lưu ý quan trọng

### Về độ chính xác
- Tool chỉ mang tính **tham khảo**
- Kết quả game slot là **hoàn toàn ngẫu nhiên**
- Không có thuật toán nào dự đoán chính xác 100%

### Chơi có trách nhiệm
- Luôn đặt giới hạn cho bản thân
- Không chơi quá khả năng tài chính
- Nghỉ ngơi khi streak quá dài
- Coi game như giải trí, không phải đầu tư

### Bảo mật dữ liệu
- Dữ liệu được lưu cục bộ trên máy tính
- Không gửi thông tin lên server
- Có thể mã hóa dữ liệu trong cài đặt

## 🛠️ Xử lý sự cố

### Lỗi khởi động
1. **Thiếu dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Lỗi Python version**:
   - Cài đặt Python 3.7 trở lên

3. **Lỗi quyền truy cập**:
   - Chạy với quyền Administrator

### Lỗi trong quá trình sử dụng
1. **Database bị lỗi**:
   - Xóa file `scatter_data.db`
   - Khởi động lại tool

2. **Config bị lỗi**:
   - Xóa file `config.json`
   - Tool sẽ tạo config mặc định

3. **Giao diện bị lỗi**:
   - Khởi động lại ứng dụng
   - Kiểm tra độ phân giải màn hình

### Lỗi chế độ tự động
1. **Không đọc được game**:
   - Kiểm tra trình duyệt được hỗ trợ
   - Tắt các extension chặn quảng cáo
   - Chạy trình duyệt với quyền Administrator

2. **Selenium lỗi**:
   ```bash
   pip install --upgrade selenium
   pip install undetected-chromedriver
   ```

### Liên hệ hỗ trợ
- Kiểm tra file log trong thư mục `logs/`
- Ghi lại các bước tái hiện lỗi
- Chụp ảnh màn hình lỗi

## 📊 Tips sử dụng hiệu quả

### Ghi nhận chính xác
- Luôn ghi nhận ngay sau mỗi spin
- Không bỏ sót bất kỳ kết quả nào
- Kiểm tra lại thống kê định kỳ

### Phân tích dữ liệu
- Cần ít nhất 100 spin để có dự đoán ổn định
- Theo dõi tỷ lệ thực tế vs tỷ lệ game
- Chú ý đến pattern theo giờ

### Quản lý rủi ro
- Đặt giới hạn thua trước khi chơi
- Nghỉ ngơi khi streak quá dài
- Không tăng cược khi thua liên tiếp

---

**🍀 Chúc bạn may mắn và chơi có trách nhiệm!**
