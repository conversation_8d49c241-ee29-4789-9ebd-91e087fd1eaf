#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Manager for PGGame Scatter Predictor
<PERSON><PERSON><PERSON><PERSON> lý cấu hình cho tool dự đoán scatter
"""

import json
import os
from typing import Dict, Any, Optional
import configparser
from datetime import datetime

class ConfigManager:
    """Manages application configuration and settings"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_settings = {
            # Game settings
            'scatter_rate': 3.0,  # Default scatter rate percentage
            'min_scatters': 3,    # Minimum scatters needed for bonus
            'game_name': 'PGGame',
            
            # Prediction settings
            'prediction_algorithm': 'advanced',
            'confidence_threshold': 0.7,
            'risk_tolerance': 'medium',
            
            # UI settings
            'theme': 'dark',
            'language': 'vi',
            'auto_save': True,
            'sound_enabled': True,
            'notifications_enabled': True,
            
            # Auto mode settings
            'auto_mode_enabled': False,
            'auto_scan_interval': 5,  # seconds
            'auto_bet_enabled': False,
            'max_auto_spins': 100,
            
            # Data settings
            'max_history_records': 10000,
            'backup_enabled': True,
            'backup_interval': 24,  # hours
            
            # Advanced settings
            'streak_adjustment_factor': 0.1,
            'pattern_analysis_enabled': True,
            'time_based_analysis': True,
            'machine_learning_enabled': False,
            
            # Security settings
            'data_encryption': False,
            'session_timeout': 3600,  # seconds
            
            # Display settings
            'show_probabilities': True,
            'show_recommendations': True,
            'show_risk_level': True,
            'decimal_places': 2,
            
            # Export/Import settings
            'export_format': 'json',
            'include_predictions': True,
            'include_sessions': True
        }
        
        self.settings = self.load_settings()
    
    def load_settings(self) -> Dict[str, Any]:
        """Load settings from config file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # Merge with defaults (add any missing keys)
                settings = self.default_settings.copy()
                settings.update(loaded_settings)
                
                return settings
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"Error loading config: {e}")
                return self.default_settings.copy()
        else:
            # Create default config file
            self.save_settings(self.default_settings)
            return self.default_settings.copy()
    
    def save_settings(self, settings: Optional[Dict[str, Any]] = None):
        """Save settings to config file"""
        if settings is not None:
            self.settings.update(settings)
        
        # Add metadata
        config_data = {
            'metadata': {
                'version': '1.0',
                'last_updated': datetime.now().isoformat(),
                'created_by': 'PGGame Scatter Predictor'
            },
            'settings': self.settings
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get_settings(self) -> Dict[str, Any]:
        """Get all current settings"""
        return self.settings.copy()
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific setting value"""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """Set a specific setting value"""
        self.settings[key] = value
        self.save_settings()
    
    def reset_to_defaults(self):
        """Reset all settings to default values"""
        self.settings = self.default_settings.copy()
        self.save_settings()
    
    def get_game_settings(self) -> Dict[str, Any]:
        """Get game-specific settings"""
        return {
            'scatter_rate': self.settings['scatter_rate'],
            'min_scatters': self.settings['min_scatters'],
            'game_name': self.settings['game_name']
        }
    
    def get_prediction_settings(self) -> Dict[str, Any]:
        """Get prediction-specific settings"""
        return {
            'algorithm': self.settings['prediction_algorithm'],
            'confidence_threshold': self.settings['confidence_threshold'],
            'risk_tolerance': self.settings['risk_tolerance'],
            'streak_adjustment_factor': self.settings['streak_adjustment_factor'],
            'pattern_analysis_enabled': self.settings['pattern_analysis_enabled'],
            'time_based_analysis': self.settings['time_based_analysis']
        }
    
    def get_ui_settings(self) -> Dict[str, Any]:
        """Get UI-specific settings"""
        return {
            'theme': self.settings['theme'],
            'language': self.settings['language'],
            'sound_enabled': self.settings['sound_enabled'],
            'notifications_enabled': self.settings['notifications_enabled'],
            'show_probabilities': self.settings['show_probabilities'],
            'show_recommendations': self.settings['show_recommendations'],
            'show_risk_level': self.settings['show_risk_level'],
            'decimal_places': self.settings['decimal_places']
        }
    
    def get_auto_mode_settings(self) -> Dict[str, Any]:
        """Get auto mode settings"""
        return {
            'enabled': self.settings['auto_mode_enabled'],
            'scan_interval': self.settings['auto_scan_interval'],
            'bet_enabled': self.settings['auto_bet_enabled'],
            'max_spins': self.settings['max_auto_spins']
        }
    
    def validate_settings(self) -> Dict[str, str]:
        """Validate current settings and return any errors"""
        errors = {}
        
        # Validate scatter rate
        if not 0.1 <= self.settings['scatter_rate'] <= 50.0:
            errors['scatter_rate'] = "Tỷ lệ scatter phải từ 0.1% đến 50%"
        
        # Validate min scatters
        if not 1 <= self.settings['min_scatters'] <= 10:
            errors['min_scatters'] = "Số scatter tối thiểu phải từ 1 đến 10"
        
        # Validate confidence threshold
        if not 0.0 <= self.settings['confidence_threshold'] <= 1.0:
            errors['confidence_threshold'] = "Ngưỡng tin cậy phải từ 0.0 đến 1.0"
        
        # Validate auto scan interval
        if not 1 <= self.settings['auto_scan_interval'] <= 60:
            errors['auto_scan_interval'] = "Khoảng thời gian quét phải từ 1 đến 60 giây"
        
        # Validate max auto spins
        if not 1 <= self.settings['max_auto_spins'] <= 10000:
            errors['max_auto_spins'] = "Số spin tự động tối đa phải từ 1 đến 10000"
        
        return errors
    
    def export_config(self, filepath: str):
        """Export configuration to file"""
        export_data = {
            'export_info': {
                'exported_at': datetime.now().isoformat(),
                'version': '1.0',
                'tool': 'PGGame Scatter Predictor'
            },
            'settings': self.settings
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error exporting config: {e}")
            return False
    
    def import_config(self, filepath: str) -> bool:
        """Import configuration from file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if 'settings' in import_data:
                # Validate imported settings
                imported_settings = import_data['settings']
                
                # Only import valid settings
                for key, value in imported_settings.items():
                    if key in self.default_settings:
                        self.settings[key] = value
                
                # Validate and save
                errors = self.validate_settings()
                if not errors:
                    self.save_settings()
                    return True
                else:
                    print(f"Validation errors: {errors}")
                    return False
            else:
                print("Invalid config file format")
                return False
                
        except Exception as e:
            print(f"Error importing config: {e}")
            return False
    
    def get_backup_settings(self) -> Dict[str, Any]:
        """Get backup-related settings"""
        return {
            'enabled': self.settings['backup_enabled'],
            'interval': self.settings['backup_interval'],
            'max_records': self.settings['max_history_records']
        }
    
    def create_profile(self, profile_name: str) -> bool:
        """Create a new settings profile"""
        profile_file = f"profile_{profile_name}.json"
        
        if os.path.exists(profile_file):
            return False  # Profile already exists
        
        try:
            profile_data = {
                'profile_info': {
                    'name': profile_name,
                    'created_at': datetime.now().isoformat(),
                    'version': '1.0'
                },
                'settings': self.settings.copy()
            }
            
            with open(profile_file, 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"Error creating profile: {e}")
            return False
    
    def load_profile(self, profile_name: str) -> bool:
        """Load settings from a profile"""
        profile_file = f"profile_{profile_name}.json"
        
        if not os.path.exists(profile_file):
            return False
        
        try:
            with open(profile_file, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            
            if 'settings' in profile_data:
                self.settings = profile_data['settings']
                self.save_settings()
                return True
            else:
                return False
                
        except Exception as e:
            print(f"Error loading profile: {e}")
            return False
    
    def list_profiles(self) -> list:
        """List all available profiles"""
        profiles = []
        for file in os.listdir('.'):
            if file.startswith('profile_') and file.endswith('.json'):
                profile_name = file[8:-5]  # Remove 'profile_' and '.json'
                profiles.append(profile_name)
        return profiles
    
    def delete_profile(self, profile_name: str) -> bool:
        """Delete a settings profile"""
        profile_file = f"profile_{profile_name}.json"
        
        try:
            if os.path.exists(profile_file):
                os.remove(profile_file)
                return True
            else:
                return False
        except Exception as e:
            print(f"Error deleting profile: {e}")
            return False
